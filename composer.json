{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "bezhansalleh/filament-shield": "^3.3", "cknow/laravel-money": "^8.4", "filament/filament": "^3.2", "giggsey/libphonenumber-for-php": "^8.13", "google/cloud-compute": "^1.30", "google/cloud-secret-manager": "^2.0", "google/cloud-sql-admin": "^1.2", "inertiajs/inertia-laravel": "^2.0", "jenssegers/agent": "^2.6", "laravel/fortify": "^1.25", "laravel/framework": "^11.31", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.20", "laravel/tinker": "^2.9", "laravelcm/laravel-subscriptions": "^1.4", "malzariey/filament-daterangepicker-filter": "^3.2.0", "owen-it/laravel-auditing": "^13.7", "propaganistas/laravel-phone": "^5.3", "rawilk/filament-password-input": "^2.0", "socialiteproviders/microsoft-azure": "^5.2", "solution-forest/filament-cms-website-plugin": "^2.1", "spatie/laravel-google-cloud-storage": "^2.3", "tangodev-it/filament-emoji-picker": "^1.0", "tapp/filament-auditing": "^3.0", "tightenco/ziggy": "^2.0", "tomatophp/filament-subscriptions": "^1.0", "tymon/jwt-auth": "^2.1", "ysfkaya/filament-phone-input": "^3.1"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/breeze": "^2.3", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://filament-cms-website-plugin.composer.sh"}]}