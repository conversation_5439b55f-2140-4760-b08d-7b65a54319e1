(()=>{var P="top",$="bottom",_="right",I="left",Xe="auto",de=[P,$,_,I],ae="start",ge="end",Yt="clippingParents",Ye="viewport",Pe="popper",Zt="reference",ut=de.reduce(function(t,e){return t.concat([e+"-"+ae,e+"-"+ge])},[]),Ze=[].concat(de,[Xe]).reduce(function(t,e){return t.concat([e,e+"-"+ae,e+"-"+ge])},[]),Yo="beforeRead",Zo="read",Jo="afterRead",Qo="beforeMain",er="main",tr="afterMain",or="beforeWrite",rr="write",nr="afterWrite",Jt=[<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>o,er,tr,or,rr,nr];function F(t){return t?(t.nodeName||"").toLowerCase():null}function C(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function X(t){var e=C(t).Element;return t instanceof e||t instanceof Element}function N(t){var e=C(t).HTMLElement;return t instanceof e||t instanceof HTMLElement}function De(t){if(typeof ShadowRoot>"u")return!1;var e=C(t).ShadowRoot;return t instanceof e||t instanceof ShadowRoot}function ir(t){var e=t.state;Object.keys(e.elements).forEach(function(o){var n=e.styles[o]||{},i=e.attributes[o]||{},r=e.elements[o];!N(r)||!F(r)||(Object.assign(r.style,n),Object.keys(i).forEach(function(a){var c=i[a];c===!1?r.removeAttribute(a):r.setAttribute(a,c===!0?"":c)}))})}function ar(t){var e=t.state,o={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,o.popper),e.styles=o,e.elements.arrow&&Object.assign(e.elements.arrow.style,o.arrow),function(){Object.keys(e.elements).forEach(function(n){var i=e.elements[n],r=e.attributes[n]||{},a=Object.keys(e.styles.hasOwnProperty(n)?e.styles[n]:o[n]),c=a.reduce(function(s,u){return s[u]="",s},{});!N(i)||!F(i)||(Object.assign(i.style,c),Object.keys(r).forEach(function(s){i.removeAttribute(s)}))})}}var Qt={name:"applyStyles",enabled:!0,phase:"write",fn:ir,effect:ar,requires:["computeStyles"]};function W(t){return t.split("-")[0]}var Q=Math.max,ve=Math.min,se=Math.round;function Le(){var t=navigator.userAgentData;return t!=null&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function Ne(){return!/^((?!chrome|android).)*safari/i.test(Le())}function Y(t,e,o){e===void 0&&(e=!1),o===void 0&&(o=!1);var n=t.getBoundingClientRect(),i=1,r=1;e&&N(t)&&(i=t.offsetWidth>0&&se(n.width)/t.offsetWidth||1,r=t.offsetHeight>0&&se(n.height)/t.offsetHeight||1);var a=X(t)?C(t):window,c=a.visualViewport,s=!Ne()&&o,u=(n.left+(s&&c?c.offsetLeft:0))/i,l=(n.top+(s&&c?c.offsetTop:0))/r,p=n.width/i,f=n.height/r;return{width:p,height:f,top:l,right:u+p,bottom:l+f,left:u,x:u,y:l}}function be(t){var e=Y(t),o=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-o)<=1&&(o=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:o,height:n}}function Fe(t,e){var o=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(o&&De(o)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function G(t){return C(t).getComputedStyle(t)}function dt(t){return["table","td","th"].indexOf(F(t))>=0}function U(t){return((X(t)?t.ownerDocument:t.document)||window.document).documentElement}function ce(t){return F(t)==="html"?t:t.assignedSlot||t.parentNode||(De(t)?t.host:null)||U(t)}function eo(t){return!N(t)||G(t).position==="fixed"?null:t.offsetParent}function sr(t){var e=/firefox/i.test(Le()),o=/Trident/i.test(Le());if(o&&N(t)){var n=G(t);if(n.position==="fixed")return null}var i=ce(t);for(De(i)&&(i=i.host);N(i)&&["html","body"].indexOf(F(i))<0;){var r=G(i);if(r.transform!=="none"||r.perspective!=="none"||r.contain==="paint"||["transform","perspective"].indexOf(r.willChange)!==-1||e&&r.willChange==="filter"||e&&r.filter&&r.filter!=="none")return i;i=i.parentNode}return null}function ee(t){for(var e=C(t),o=eo(t);o&&dt(o)&&G(o).position==="static";)o=eo(o);return o&&(F(o)==="html"||F(o)==="body"&&G(o).position==="static")?e:o||sr(t)||e}function ye(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function we(t,e,o){return Q(t,ve(e,o))}function to(t,e,o){var n=we(t,e,o);return n>o?o:n}function We(){return{top:0,right:0,bottom:0,left:0}}function ze(t){return Object.assign({},We(),t)}function Ue(t,e){return e.reduce(function(o,n){return o[n]=t,o},{})}var cr=function(e,o){return e=typeof e=="function"?e(Object.assign({},o.rects,{placement:o.placement})):e,ze(typeof e!="number"?e:Ue(e,de))};function lr(t){var e,o=t.state,n=t.name,i=t.options,r=o.elements.arrow,a=o.modifiersData.popperOffsets,c=W(o.placement),s=ye(c),u=[I,_].indexOf(c)>=0,l=u?"height":"width";if(!(!r||!a)){var p=cr(i.padding,o),f=be(r),m=s==="y"?P:I,j=s==="y"?$:_,b=o.rects.reference[l]+o.rects.reference[s]-a[s]-o.rects.popper[l],g=a[s]-o.rects.reference[s],v=ee(r),k=v?s==="y"?v.clientHeight||0:v.clientWidth||0:0,x=b/2-g/2,w=p[m],T=k-f[l]-p[j],S=k/2-f[l]/2+x,O=we(w,S,T),M=s;o.modifiersData[n]=(e={},e[M]=O,e.centerOffset=O-S,e)}}function ur(t){var e=t.state,o=t.options,n=o.element,i=n===void 0?"[data-popper-arrow]":n;i!=null&&(typeof i=="string"&&(i=e.elements.popper.querySelector(i),!i)||Fe(e.elements.popper,i)&&(e.elements.arrow=i))}var oo={name:"arrow",enabled:!0,phase:"main",fn:lr,effect:ur,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Z(t){return t.split("-")[1]}var dr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function fr(t,e){var o=t.x,n=t.y,i=e.devicePixelRatio||1;return{x:se(o*i)/i||0,y:se(n*i)/i||0}}function ro(t){var e,o=t.popper,n=t.popperRect,i=t.placement,r=t.variation,a=t.offsets,c=t.position,s=t.gpuAcceleration,u=t.adaptive,l=t.roundOffsets,p=t.isFixed,f=a.x,m=f===void 0?0:f,j=a.y,b=j===void 0?0:j,g=typeof l=="function"?l({x:m,y:b}):{x:m,y:b};m=g.x,b=g.y;var v=a.hasOwnProperty("x"),k=a.hasOwnProperty("y"),x=I,w=P,T=window;if(u){var S=ee(o),O="clientHeight",M="clientWidth";if(S===C(o)&&(S=U(o),G(S).position!=="static"&&c==="absolute"&&(O="scrollHeight",M="scrollWidth")),S=S,i===P||(i===I||i===_)&&r===ge){w=$;var B=p&&S===T&&T.visualViewport?T.visualViewport.height:S[O];b-=B-n.height,b*=s?1:-1}if(i===I||(i===P||i===$)&&r===ge){x=_;var A=p&&S===T&&T.visualViewport?T.visualViewport.width:S[M];m-=A-n.width,m*=s?1:-1}}var R=Object.assign({position:c},u&&dr),q=l===!0?fr({x:m,y:b},C(o)):{x:m,y:b};if(m=q.x,b=q.y,s){var z;return Object.assign({},R,(z={},z[w]=k?"0":"",z[x]=v?"0":"",z.transform=(T.devicePixelRatio||1)<=1?"translate("+m+"px, "+b+"px)":"translate3d("+m+"px, "+b+"px, 0)",z))}return Object.assign({},R,(e={},e[w]=k?b+"px":"",e[x]=v?m+"px":"",e.transform="",e))}function pr(t){var e=t.state,o=t.options,n=o.gpuAcceleration,i=n===void 0?!0:n,r=o.adaptive,a=r===void 0?!0:r,c=o.roundOffsets,s=c===void 0?!0:c,u={placement:W(e.placement),variation:Z(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,ro(Object.assign({},u,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:a,roundOffsets:s})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,ro(Object.assign({},u,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}var no={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:pr,data:{}};var Je={passive:!0};function mr(t){var e=t.state,o=t.instance,n=t.options,i=n.scroll,r=i===void 0?!0:i,a=n.resize,c=a===void 0?!0:a,s=C(e.elements.popper),u=[].concat(e.scrollParents.reference,e.scrollParents.popper);return r&&u.forEach(function(l){l.addEventListener("scroll",o.update,Je)}),c&&s.addEventListener("resize",o.update,Je),function(){r&&u.forEach(function(l){l.removeEventListener("scroll",o.update,Je)}),c&&s.removeEventListener("resize",o.update,Je)}}var io={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:mr,data:{}};var hr={left:"right",right:"left",bottom:"top",top:"bottom"};function Ie(t){return t.replace(/left|right|bottom|top/g,function(e){return hr[e]})}var gr={start:"end",end:"start"};function Qe(t){return t.replace(/start|end/g,function(e){return gr[e]})}function xe(t){var e=C(t),o=e.pageXOffset,n=e.pageYOffset;return{scrollLeft:o,scrollTop:n}}function Ee(t){return Y(U(t)).left+xe(t).scrollLeft}function ft(t,e){var o=C(t),n=U(t),i=o.visualViewport,r=n.clientWidth,a=n.clientHeight,c=0,s=0;if(i){r=i.width,a=i.height;var u=Ne();(u||!u&&e==="fixed")&&(c=i.offsetLeft,s=i.offsetTop)}return{width:r,height:a,x:c+Ee(t),y:s}}function pt(t){var e,o=U(t),n=xe(t),i=(e=t.ownerDocument)==null?void 0:e.body,r=Q(o.scrollWidth,o.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),a=Q(o.scrollHeight,o.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),c=-n.scrollLeft+Ee(t),s=-n.scrollTop;return G(i||o).direction==="rtl"&&(c+=Q(o.clientWidth,i?i.clientWidth:0)-r),{width:r,height:a,x:c,y:s}}function ke(t){var e=G(t),o=e.overflow,n=e.overflowX,i=e.overflowY;return/auto|scroll|overlay|hidden/.test(o+i+n)}function et(t){return["html","body","#document"].indexOf(F(t))>=0?t.ownerDocument.body:N(t)&&ke(t)?t:et(ce(t))}function fe(t,e){var o;e===void 0&&(e=[]);var n=et(t),i=n===((o=t.ownerDocument)==null?void 0:o.body),r=C(n),a=i?[r].concat(r.visualViewport||[],ke(n)?n:[]):n,c=e.concat(a);return i?c:c.concat(fe(ce(a)))}function Be(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function vr(t,e){var o=Y(t,!1,e==="fixed");return o.top=o.top+t.clientTop,o.left=o.left+t.clientLeft,o.bottom=o.top+t.clientHeight,o.right=o.left+t.clientWidth,o.width=t.clientWidth,o.height=t.clientHeight,o.x=o.left,o.y=o.top,o}function ao(t,e,o){return e===Ye?Be(ft(t,o)):X(e)?vr(e,o):Be(pt(U(t)))}function br(t){var e=fe(ce(t)),o=["absolute","fixed"].indexOf(G(t).position)>=0,n=o&&N(t)?ee(t):t;return X(n)?e.filter(function(i){return X(i)&&Fe(i,n)&&F(i)!=="body"}):[]}function mt(t,e,o,n){var i=e==="clippingParents"?br(t):[].concat(e),r=[].concat(i,[o]),a=r[0],c=r.reduce(function(s,u){var l=ao(t,u,n);return s.top=Q(l.top,s.top),s.right=ve(l.right,s.right),s.bottom=ve(l.bottom,s.bottom),s.left=Q(l.left,s.left),s},ao(t,a,n));return c.width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c}function He(t){var e=t.reference,o=t.element,n=t.placement,i=n?W(n):null,r=n?Z(n):null,a=e.x+e.width/2-o.width/2,c=e.y+e.height/2-o.height/2,s;switch(i){case P:s={x:a,y:e.y-o.height};break;case $:s={x:a,y:e.y+e.height};break;case _:s={x:e.x+e.width,y:c};break;case I:s={x:e.x-o.width,y:c};break;default:s={x:e.x,y:e.y}}var u=i?ye(i):null;if(u!=null){var l=u==="y"?"height":"width";switch(r){case ae:s[u]=s[u]-(e[l]/2-o[l]/2);break;case ge:s[u]=s[u]+(e[l]/2-o[l]/2);break;default:}}return s}function te(t,e){e===void 0&&(e={});var o=e,n=o.placement,i=n===void 0?t.placement:n,r=o.strategy,a=r===void 0?t.strategy:r,c=o.boundary,s=c===void 0?Yt:c,u=o.rootBoundary,l=u===void 0?Ye:u,p=o.elementContext,f=p===void 0?Pe:p,m=o.altBoundary,j=m===void 0?!1:m,b=o.padding,g=b===void 0?0:b,v=ze(typeof g!="number"?g:Ue(g,de)),k=f===Pe?Zt:Pe,x=t.rects.popper,w=t.elements[j?k:f],T=mt(X(w)?w:w.contextElement||U(t.elements.popper),s,l,a),S=Y(t.elements.reference),O=He({reference:S,element:x,strategy:"absolute",placement:i}),M=Be(Object.assign({},x,O)),B=f===Pe?M:S,A={top:T.top-B.top+v.top,bottom:B.bottom-T.bottom+v.bottom,left:T.left-B.left+v.left,right:B.right-T.right+v.right},R=t.modifiersData.offset;if(f===Pe&&R){var q=R[i];Object.keys(A).forEach(function(z){var ne=[_,$].indexOf(z)>=0?1:-1,ie=[P,$].indexOf(z)>=0?"y":"x";A[z]+=q[ie]*ne})}return A}function ht(t,e){e===void 0&&(e={});var o=e,n=o.placement,i=o.boundary,r=o.rootBoundary,a=o.padding,c=o.flipVariations,s=o.allowedAutoPlacements,u=s===void 0?Ze:s,l=Z(n),p=l?c?ut:ut.filter(function(j){return Z(j)===l}):de,f=p.filter(function(j){return u.indexOf(j)>=0});f.length===0&&(f=p);var m=f.reduce(function(j,b){return j[b]=te(t,{placement:b,boundary:i,rootBoundary:r,padding:a})[W(b)],j},{});return Object.keys(m).sort(function(j,b){return m[j]-m[b]})}function yr(t){if(W(t)===Xe)return[];var e=Ie(t);return[Qe(t),e,Qe(e)]}function wr(t){var e=t.state,o=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var i=o.mainAxis,r=i===void 0?!0:i,a=o.altAxis,c=a===void 0?!0:a,s=o.fallbackPlacements,u=o.padding,l=o.boundary,p=o.rootBoundary,f=o.altBoundary,m=o.flipVariations,j=m===void 0?!0:m,b=o.allowedAutoPlacements,g=e.options.placement,v=W(g),k=v===g,x=s||(k||!j?[Ie(g)]:yr(g)),w=[g].concat(x).reduce(function(ue,oe){return ue.concat(W(oe)===Xe?ht(e,{placement:oe,boundary:l,rootBoundary:p,padding:u,flipVariations:j,allowedAutoPlacements:b}):oe)},[]),T=e.rects.reference,S=e.rects.popper,O=new Map,M=!0,B=w[0],A=0;A<w.length;A++){var R=w[A],q=W(R),z=Z(R)===ae,ne=[P,$].indexOf(q)>=0,ie=ne?"width":"height",H=te(e,{placement:R,boundary:l,rootBoundary:p,altBoundary:f,padding:u}),K=ne?z?_:I:z?$:P;T[ie]>S[ie]&&(K=Ie(K));var Oe=Ie(K),J=[];if(r&&J.push(H[q]<=0),c&&J.push(H[K]<=0,H[Oe]<=0),J.every(function(ue){return ue})){B=R,M=!1;break}O.set(R,J)}if(M)for(var Ce=j?3:1,$e=function(oe){var d=w.find(function(h){var y=O.get(h);if(y)return y.slice(0,oe).every(function(E){return E})});if(d)return B=d,"break"},pe=Ce;pe>0;pe--){var Ae=$e(pe);if(Ae==="break")break}e.placement!==B&&(e.modifiersData[n]._skip=!0,e.placement=B,e.reset=!0)}}var so={name:"flip",enabled:!0,phase:"main",fn:wr,requiresIfExists:["offset"],data:{_skip:!1}};function co(t,e,o){return o===void 0&&(o={x:0,y:0}),{top:t.top-e.height-o.y,right:t.right-e.width+o.x,bottom:t.bottom-e.height+o.y,left:t.left-e.width-o.x}}function lo(t){return[P,_,$,I].some(function(e){return t[e]>=0})}function xr(t){var e=t.state,o=t.name,n=e.rects.reference,i=e.rects.popper,r=e.modifiersData.preventOverflow,a=te(e,{elementContext:"reference"}),c=te(e,{altBoundary:!0}),s=co(a,n),u=co(c,i,r),l=lo(s),p=lo(u);e.modifiersData[o]={referenceClippingOffsets:s,popperEscapeOffsets:u,isReferenceHidden:l,hasPopperEscaped:p},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":l,"data-popper-escaped":p})}var uo={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:xr};function Er(t,e,o){var n=W(t),i=[I,P].indexOf(n)>=0?-1:1,r=typeof o=="function"?o(Object.assign({},e,{placement:t})):o,a=r[0],c=r[1];return a=a||0,c=(c||0)*i,[I,_].indexOf(n)>=0?{x:c,y:a}:{x:a,y:c}}function kr(t){var e=t.state,o=t.options,n=t.name,i=o.offset,r=i===void 0?[0,0]:i,a=Ze.reduce(function(l,p){return l[p]=Er(p,e.rects,r),l},{}),c=a[e.placement],s=c.x,u=c.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=s,e.modifiersData.popperOffsets.y+=u),e.modifiersData[n]=a}var fo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:kr};function jr(t){var e=t.state,o=t.name;e.modifiersData[o]=He({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}var po={name:"popperOffsets",enabled:!0,phase:"read",fn:jr,data:{}};function gt(t){return t==="x"?"y":"x"}function Tr(t){var e=t.state,o=t.options,n=t.name,i=o.mainAxis,r=i===void 0?!0:i,a=o.altAxis,c=a===void 0?!1:a,s=o.boundary,u=o.rootBoundary,l=o.altBoundary,p=o.padding,f=o.tether,m=f===void 0?!0:f,j=o.tetherOffset,b=j===void 0?0:j,g=te(e,{boundary:s,rootBoundary:u,padding:p,altBoundary:l}),v=W(e.placement),k=Z(e.placement),x=!k,w=ye(v),T=gt(w),S=e.modifiersData.popperOffsets,O=e.rects.reference,M=e.rects.popper,B=typeof b=="function"?b(Object.assign({},e.rects,{placement:e.placement})):b,A=typeof B=="number"?{mainAxis:B,altAxis:B}:Object.assign({mainAxis:0,altAxis:0},B),R=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,q={x:0,y:0};if(S){if(r){var z,ne=w==="y"?P:I,ie=w==="y"?$:_,H=w==="y"?"height":"width",K=S[w],Oe=K+g[ne],J=K-g[ie],Ce=m?-M[H]/2:0,$e=k===ae?O[H]:M[H],pe=k===ae?-M[H]:-O[H],Ae=e.elements.arrow,ue=m&&Ae?be(Ae):{width:0,height:0},oe=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:We(),d=oe[ne],h=oe[ie],y=we(0,O[H],ue[H]),E=x?O[H]/2-Ce-y-d-A.mainAxis:$e-y-d-A.mainAxis,D=x?-O[H]/2+Ce+y+h+A.mainAxis:pe+y+h+A.mainAxis,L=e.elements.arrow&&ee(e.elements.arrow),V=L?w==="y"?L.clientTop||0:L.clientLeft||0:0,me=(z=R?.[w])!=null?z:0,Go=K+E-me-V,Ko=K+D-me,zt=we(m?ve(Oe,Go):Oe,K,m?Q(J,Ko):J);S[w]=zt,q[w]=zt-K}if(c){var Ut,qo=w==="x"?P:I,Xo=w==="x"?$:_,he=S[T],qe=T==="y"?"height":"width",Ht=he+g[qo],Vt=he-g[Xo],lt=[P,I].indexOf(v)!==-1,Gt=(Ut=R?.[T])!=null?Ut:0,Kt=lt?Ht:he-O[qe]-M[qe]-Gt+A.altAxis,qt=lt?he+O[qe]+M[qe]-Gt-A.altAxis:Vt,Xt=m&&lt?to(Kt,he,qt):we(m?Kt:Ht,he,m?qt:Vt);S[T]=Xt,q[T]=Xt-he}e.modifiersData[n]=q}}var mo={name:"preventOverflow",enabled:!0,phase:"main",fn:Tr,requiresIfExists:["offset"]};function vt(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function bt(t){return t===C(t)||!N(t)?xe(t):vt(t)}function Sr(t){var e=t.getBoundingClientRect(),o=se(e.width)/t.offsetWidth||1,n=se(e.height)/t.offsetHeight||1;return o!==1||n!==1}function yt(t,e,o){o===void 0&&(o=!1);var n=N(e),i=N(e)&&Sr(e),r=U(e),a=Y(t,i,o),c={scrollLeft:0,scrollTop:0},s={x:0,y:0};return(n||!n&&!o)&&((F(e)!=="body"||ke(r))&&(c=bt(e)),N(e)?(s=Y(e,!0),s.x+=e.clientLeft,s.y+=e.clientTop):r&&(s.x=Ee(r))),{x:a.left+c.scrollLeft-s.x,y:a.top+c.scrollTop-s.y,width:a.width,height:a.height}}function Or(t){var e=new Map,o=new Set,n=[];t.forEach(function(r){e.set(r.name,r)});function i(r){o.add(r.name);var a=[].concat(r.requires||[],r.requiresIfExists||[]);a.forEach(function(c){if(!o.has(c)){var s=e.get(c);s&&i(s)}}),n.push(r)}return t.forEach(function(r){o.has(r.name)||i(r)}),n}function wt(t){var e=Or(t);return Jt.reduce(function(o,n){return o.concat(e.filter(function(i){return i.phase===n}))},[])}function xt(t){var e;return function(){return e||(e=new Promise(function(o){Promise.resolve().then(function(){e=void 0,o(t())})})),e}}function Et(t){var e=t.reduce(function(o,n){var i=o[n.name];return o[n.name]=i?Object.assign({},i,n,{options:Object.assign({},i.options,n.options),data:Object.assign({},i.data,n.data)}):n,o},{});return Object.keys(e).map(function(o){return e[o]})}var ho={placement:"bottom",modifiers:[],strategy:"absolute"};function go(){for(var t=arguments.length,e=new Array(t),o=0;o<t;o++)e[o]=arguments[o];return!e.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function vo(t){t===void 0&&(t={});var e=t,o=e.defaultModifiers,n=o===void 0?[]:o,i=e.defaultOptions,r=i===void 0?ho:i;return function(c,s,u){u===void 0&&(u=r);var l={placement:"bottom",orderedModifiers:[],options:Object.assign({},ho,r),modifiersData:{},elements:{reference:c,popper:s},attributes:{},styles:{}},p=[],f=!1,m={state:l,setOptions:function(v){var k=typeof v=="function"?v(l.options):v;b(),l.options=Object.assign({},r,l.options,k),l.scrollParents={reference:X(c)?fe(c):c.contextElement?fe(c.contextElement):[],popper:fe(s)};var x=wt(Et([].concat(n,l.options.modifiers)));return l.orderedModifiers=x.filter(function(w){return w.enabled}),j(),m.update()},forceUpdate:function(){if(!f){var v=l.elements,k=v.reference,x=v.popper;if(go(k,x)){l.rects={reference:yt(k,ee(x),l.options.strategy==="fixed"),popper:be(x)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(A){return l.modifiersData[A.name]=Object.assign({},A.data)});for(var w=0;w<l.orderedModifiers.length;w++){if(l.reset===!0){l.reset=!1,w=-1;continue}var T=l.orderedModifiers[w],S=T.fn,O=T.options,M=O===void 0?{}:O,B=T.name;typeof S=="function"&&(l=S({state:l,options:M,name:B,instance:m})||l)}}}},update:xt(function(){return new Promise(function(g){m.forceUpdate(),g(l)})}),destroy:function(){b(),f=!0}};if(!go(c,s))return m;m.setOptions(u).then(function(g){!f&&u.onFirstUpdate&&u.onFirstUpdate(g)});function j(){l.orderedModifiers.forEach(function(g){var v=g.name,k=g.options,x=k===void 0?{}:k,w=g.effect;if(typeof w=="function"){var T=w({state:l,name:v,instance:m,options:x}),S=function(){};p.push(T||S)}})}function b(){p.forEach(function(g){return g()}),p=[]}return m}}var Cr=[io,po,no,Qt,fo,so,mo,oo,uo],kt=vo({defaultModifiers:Cr});function tt(t){if(typeof t!="string"||!t)throw new Error("expected a non-empty string, got: "+t)}function jt(t){if(typeof t!="number")throw new Error("expected a number, got: "+t)}var Ar=1,Pr=1,Te="emoji",_e="keyvalue",Ct="favorites",Dr="tokens",xo="tokens",Lr="unicode",Eo="count",Ir="group",Br="order",ko="group-order",Tt="eTag",nt="url",bo="skinTone",Me="readonly",At="readwrite",jo="skinUnicodes",Rr="skinUnicodes",_r="https://cdn.jsdelivr.net/npm/emoji-picker-element-data@^1/en/emojibase/data.json",Mr="en";function $r(t,e){let o=new Set,n=[];for(let i of t){let r=e(i);o.has(r)||(o.add(r),n.push(i))}return n}function yo(t){return $r(t,e=>e.unicode)}function Nr(t){function e(o,n,i){let r=n?t.createObjectStore(o,{keyPath:n}):t.createObjectStore(o);if(i)for(let[a,[c,s]]of Object.entries(i))r.createIndex(a,c,{multiEntry:s});return r}e(_e),e(Te,Lr,{[xo]:[Dr,!0],[ko]:[[Ir,Br]],[jo]:[Rr,!0]}),e(Ct,void 0,{[Eo]:[""]})}var St={},rt={},it={};function To(t,e,o){o.onerror=()=>e(o.error),o.onblocked=()=>e(new Error("IDB blocked")),o.onsuccess=()=>t(o.result)}async function Fr(t){let e=await new Promise((o,n)=>{let i=indexedDB.open(t,Ar);St[t]=i,i.onupgradeneeded=r=>{r.oldVersion<Pr&&Nr(i.result)},To(o,n,i)});return e.onclose=()=>Pt(t),e}function Wr(t){return rt[t]||(rt[t]=Fr(t)),rt[t]}function le(t,e,o,n){return new Promise((i,r)=>{let a=t.transaction(e,o,{durability:"relaxed"}),c=typeof e=="string"?a.objectStore(e):e.map(u=>a.objectStore(u)),s;n(c,a,u=>{s=u}),a.oncomplete=()=>i(s),a.onerror=()=>r(a.error)})}function Pt(t){let e=St[t],o=e&&e.result;if(o){o.close();let n=it[t];if(n)for(let i of n)i()}delete St[t],delete rt[t],delete it[t]}function zr(t){return new Promise((e,o)=>{Pt(t);let n=indexedDB.deleteDatabase(t);To(e,o,n)})}function Ur(t,e){let o=it[t];o||(o=it[t]=[]),o.push(e)}var Hr=new Set([":D","XD",":'D","O:)",":X",":P",";P","XP",":L",":Z",":j","8D","XO","8)",":B",":O",":S",":'o","Dx","X(","D:",":C",">0)",":3","</3","<3","\\M/",":E","8#"]);function Re(t){return t.split(/[\s_]+/).map(e=>!e.match(/\w/)||Hr.has(e)?e.toLowerCase():e.replace(/[)(:,]/g,"").replace(/’/g,"'").toLowerCase()).filter(Boolean)}var Vr=2;function So(t){return t.filter(Boolean).map(e=>e.toLowerCase()).filter(e=>e.length>=Vr)}function Gr(t){return t.map(({annotation:o,emoticon:n,group:i,order:r,shortcodes:a,skins:c,tags:s,emoji:u,version:l})=>{let p=[...new Set(So([...(a||[]).map(Re).flat(),...s.map(Re).flat(),...Re(o),n]))].sort(),f={annotation:o,group:i,order:r,tags:s,tokens:p,unicode:u,version:l};if(n&&(f.emoticon=n),a&&(f.shortcodes=a),c){f.skinTones=[],f.skinUnicodes=[],f.skinVersions=[];for(let{tone:m,emoji:j,version:b}of c)f.skinTones.push(m),f.skinUnicodes.push(j),f.skinVersions.push(b)}return f})}function Oo(t,e,o,n){t[e](o).onsuccess=i=>n&&n(i.target.result)}function je(t,e,o){Oo(t,"get",e,o)}function Co(t,e,o){Oo(t,"getAll",e,o)}function Dt(t){t.commit&&t.commit()}function Kr(t,e){let o=t[0];for(let n=1;n<t.length;n++){let i=t[n];e(o)>e(i)&&(o=i)}return o}function Ao(t,e){let o=Kr(t,i=>i.length),n=[];for(let i of o)t.some(r=>r.findIndex(a=>e(a)===e(i))===-1)||n.push(i);return n}async function qr(t){return!await Lt(t,_e,nt)}async function Xr(t,e,o){let[n,i]=await Promise.all([Tt,nt].map(r=>Lt(t,_e,r)));return n===o&&i===e}async function Yr(t,e){return le(t,Te,Me,(n,i,r)=>{let a,c=()=>{n.getAll(a&&IDBKeyRange.lowerBound(a,!0),50).onsuccess=s=>{let u=s.target.result;for(let l of u)if(a=l.unicode,e(l))return r(l);if(u.length<50)return r();c()}};c()})}async function Po(t,e,o,n){try{let i=Gr(e);await le(t,[Te,_e],At,([r,a],c)=>{let s,u,l=0;function p(){++l===2&&f()}function f(){if(!(s===n&&u===o)){r.clear();for(let m of i)r.put(m);a.put(n,Tt),a.put(o,nt),Dt(c)}}je(a,Tt,m=>{s=m,p()}),je(a,nt,m=>{u=m,p()})})}finally{}}async function Zr(t,e){return le(t,Te,Me,(o,n,i)=>{let r=IDBKeyRange.bound([e,0],[e+1,0],!1,!0);Co(o.index(ko),r,i)})}async function Do(t,e){let o=So(Re(e));return o.length?le(t,Te,Me,(n,i,r)=>{let a=[],c=()=>{a.length===o.length&&s()},s=()=>{let u=Ao(a,l=>l.unicode);r(u.sort((l,p)=>l.order<p.order?-1:1))};for(let u=0;u<o.length;u++){let l=o[u],p=u===o.length-1?IDBKeyRange.bound(l,l+"\uFFFF",!1,!0):IDBKeyRange.only(l);Co(n.index(xo),p,f=>{a.push(f),c()})}}):[]}async function Jr(t,e){let o=await Do(t,e);return o.length?o.filter(n=>(n.shortcodes||[]).map(r=>r.toLowerCase()).includes(e.toLowerCase()))[0]||null:await Yr(t,i=>(i.shortcodes||[]).includes(e.toLowerCase()))||null}async function Qr(t,e){return le(t,Te,Me,(o,n,i)=>je(o,e,r=>{if(r)return i(r);je(o.index(jo),e,a=>i(a||null))}))}function Lt(t,e,o){return le(t,e,Me,(n,i,r)=>je(n,o,r))}function en(t,e,o,n){return le(t,e,At,(i,r)=>{i.put(n,o),Dt(r)})}function tn(t,e){return le(t,Ct,At,(o,n)=>je(o,e,i=>{o.put((i||0)+1,e),Dt(n)}))}function on(t,e,o){return o===0?[]:le(t,[Ct,Te],Me,([n,i],r,a)=>{let c=[];n.index(Eo).openCursor(void 0,"prev").onsuccess=s=>{let u=s.target.result;if(!u)return a(c);function l(m){if(c.push(m),c.length===o)return a(c);u.continue()}let p=u.primaryKey,f=e.byName(p);if(f)return l(f);je(i,p,m=>{if(m)return l(m);u.continue()})}})}var ot="";function rn(t,e){let o=new Map;for(let i of t){let r=e(i);for(let a of r){let c=o;for(let u=0;u<a.length;u++){let l=a.charAt(u),p=c.get(l);p||(p=new Map,c.set(l,p)),c=p}let s=c.get(ot);s||(s=[],c.set(ot,s)),s.push(i)}}return(i,r)=>{let a=o;for(let u=0;u<i.length;u++){let l=i.charAt(u),p=a.get(l);if(p)a=p;else return[]}if(r)return a.get(ot)||[];let c=[],s=[a];for(;s.length;){let l=[...s.shift().entries()].sort((p,f)=>p[0]<f[0]?-1:1);for(let[p,f]of l)p===ot?c.push(...f):s.push(f)}return c}}var nn=["name","url"];function an(t){let e=t&&Array.isArray(t),o=e&&t.length&&(!t[0]||nn.some(n=>!(n in t[0])));if(!e||o)throw new Error("Custom emojis are in the wrong format")}function wo(t){an(t);let e=(f,m)=>f.name.toLowerCase()<m.name.toLowerCase()?-1:1,o=t.sort(e),i=rn(t,f=>[...new Set((f.shortcodes||[]).map(m=>Re(m)).flat())]),r=f=>i(f,!0),a=f=>i(f,!1),c=f=>{let m=Re(f),j=m.map((b,g)=>(g<m.length-1?r:a)(b));return Ao(j,b=>b.name).sort(e)},s=new Map,u=new Map;for(let f of t){u.set(f.name.toLowerCase(),f);for(let m of f.shortcodes||[])s.set(m.toLowerCase(),f)}return{all:o,search:c,byShortcode:f=>s.get(f.toLowerCase()),byName:f=>u.get(f.toLowerCase())}}var sn=typeof wrappedJSObject<"u";function Ve(t){if(!t)return t;if(sn&&(t=structuredClone(t)),delete t.tokens,t.skinTones){let e=t.skinTones.length;t.skins=Array(e);for(let o=0;o<e;o++)t.skins[o]={tone:t.skinTones[o],unicode:t.skinUnicodes[o],version:t.skinVersions[o]};delete t.skinTones,delete t.skinUnicodes,delete t.skinVersions}return t}function Lo(t){t||console.warn("emoji-picker-element is more efficient if the dataSource server exposes an ETag header.")}var cn=["annotation","emoji","group","order","tags","version"];function ln(t){if(!t||!Array.isArray(t)||!t[0]||typeof t[0]!="object"||cn.some(e=>!(e in t[0])))throw new Error("Emoji data is in the wrong format")}function Io(t,e){if(Math.floor(t.status/100)!==2)throw new Error("Failed to fetch: "+e+":  "+t.status)}async function un(t){let e=await fetch(t,{method:"HEAD"});Io(e,t);let o=e.headers.get("etag");return Lo(o),o}async function Ot(t){let e=await fetch(t);Io(e,t);let o=e.headers.get("etag");Lo(o);let n=await e.json();return ln(n),[o,n]}function dn(t){for(var e="",o=new Uint8Array(t),n=o.byteLength,i=-1;++i<n;)e+=String.fromCharCode(o[i]);return e}function fn(t){for(var e=t.length,o=new ArrayBuffer(e),n=new Uint8Array(o),i=-1;++i<e;)n[i]=t.charCodeAt(i);return o}async function Bo(t){let e=JSON.stringify(t),o=fn(e),n=await crypto.subtle.digest("SHA-1",o),i=dn(n);return btoa(i)}async function pn(t,e){let o,n=await un(e);if(!n){let i=await Ot(e);n=i[0],o=i[1],n||(n=await Bo(o))}await Xr(t,e,n)||(o||(o=(await Ot(e))[1]),await Po(t,o,e,n))}async function mn(t,e){let[o,n]=await Ot(e);o||(o=await Bo(n)),await Po(t,n,e,o)}var Ge=class{constructor({dataSource:e=_r,locale:o=Mr,customEmoji:n=[]}={}){this.dataSource=e,this.locale=o,this._dbName=`emoji-picker-element-${this.locale}`,this._db=void 0,this._lazyUpdate=void 0,this._custom=wo(n),this._clear=this._clear.bind(this),this._ready=this._init()}async _init(){let e=this._db=await Wr(this._dbName);Ur(this._dbName,this._clear);let o=this.dataSource;await qr(e)?await mn(e,o):this._lazyUpdate=pn(e,o)}async ready(){let e=async()=>(this._ready||(this._ready=this._init()),this._ready);await e(),this._db||await e()}async getEmojiByGroup(e){return jt(e),await this.ready(),yo(await Zr(this._db,e)).map(Ve)}async getEmojiBySearchQuery(e){tt(e),await this.ready();let o=this._custom.search(e),n=yo(await Do(this._db,e)).map(Ve);return[...o,...n]}async getEmojiByShortcode(e){tt(e),await this.ready();let o=this._custom.byShortcode(e);return o||Ve(await Jr(this._db,e))}async getEmojiByUnicodeOrName(e){tt(e),await this.ready();let o=this._custom.byName(e);return o||Ve(await Qr(this._db,e))}async getPreferredSkinTone(){return await this.ready(),await Lt(this._db,_e,bo)||0}async setPreferredSkinTone(e){return jt(e),await this.ready(),en(this._db,_e,bo,e)}async incrementFavoriteEmojiCount(e){return tt(e),await this.ready(),tn(this._db,e)}async getTopFavoriteEmoji(e){return jt(e),await this.ready(),(await on(this._db,this._custom,e)).map(Ve)}set customEmoji(e){this._custom=wo(e)}get customEmoji(){return this._custom.all}async _shutdown(){await this.ready();try{await this._lazyUpdate}catch{}}_clear(){this._db=this._ready=this._lazyUpdate=void 0}async close(){await this._shutdown(),await Pt(this._dbName)}async delete(){await this._shutdown(),await zr(this._dbName)}};var Ft=[[-1,"\u2728","custom"],[0,"\u{1F600}","smileys-emotion"],[1,"\u{1F44B}","people-body"],[3,"\u{1F431}","animals-nature"],[4,"\u{1F34E}","food-drink"],[5,"\u{1F3E0}\uFE0F","travel-places"],[6,"\u26BD","activities"],[7,"\u{1F4DD}","objects"],[8,"\u26D4\uFE0F","symbols"],[9,"\u{1F3C1}","flags"]].map(([t,e,o])=>({id:t,emoji:e,name:o})),It=Ft.slice(1),hn=2,Ro=6,Fo=typeof requestIdleCallback=="function"?requestIdleCallback:setTimeout;function _o(t){return t.unicode.includes("\u200D")}var gn={"\u{1FAE8}":15.1,"\u{1FAE0}":14,"\u{1F972}":13.1,"\u{1F97B}":12.1,"\u{1F970}":11,"\u{1F929}":5,"\u{1F471}\u200D\u2640\uFE0F":4,"\u{1F923}":3,"\u{1F441}\uFE0F\u200D\u{1F5E8}\uFE0F":2,"\u{1F600}":1,"\u{1F610}\uFE0F":.7,"\u{1F603}":.6},vn=1e3,bn="\u{1F590}\uFE0F",yn=8,wn=["\u{1F60A}","\u{1F612}","\u2764\uFE0F","\u{1F44D}\uFE0F","\u{1F60D}","\u{1F602}","\u{1F62D}","\u263A\uFE0F","\u{1F614}","\u{1F629}","\u{1F60F}","\u{1F495}","\u{1F64C}","\u{1F618}"],Wo='"Twemoji Mozilla","Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji","EmojiOne Color","Android Emoji",sans-serif',xn=(t,e)=>t<e?-1:t>e?1:0,Mo=(t,e)=>{let o=document.createElement("canvas");o.width=o.height=1;let n=o.getContext("2d");return n.textBaseline="top",n.font=`100px ${Wo}`,n.fillStyle=e,n.scale(.01,.01),n.fillText(t,0,0),n.getImageData(0,0,1,1).data},En=(t,e)=>{let o=[...t].join(","),n=[...e].join(",");return o===n&&!o.startsWith("0,0,0,")};function kn(t){let e=Mo(t,"#000"),o=Mo(t,"#fff");return e&&o&&En(e,o)}function jn(){let t=Object.entries(gn);try{for(let[e,o]of t)if(kn(e))return o}catch{}finally{}return t[0][1]}var Bt,Rt=()=>(Bt||(Bt=new Promise(t=>Fo(()=>t(jn())))),Bt),Wt=new Map,Tn="\uFE0F",Sn="\uD83C",On="\u200D",Cn=127995,An=57339;function Pn(t,e){if(e===0)return t;let o=t.indexOf(On);return o!==-1?t.substring(0,o)+String.fromCodePoint(Cn+e-1)+t.substring(o):(t.endsWith(Tn)&&(t=t.substring(0,t.length-1)),t+Sn+String.fromCodePoint(An+e-1))}function re(t){t.preventDefault(),t.stopPropagation()}function _t(t,e,o){return e+=t?-1:1,e<0?e=o.length-1:e>=o.length&&(e=0),e}function zo(t,e){let o=new Set,n=[];for(let i of t){let r=e(i);o.has(r)||(o.add(r),n.push(i))}return n}function Dn(t,e){let o=n=>{let i={};for(let r of n)typeof r.tone=="number"&&r.version<=e&&(i[r.tone]=r.unicode);return i};return t.map(({unicode:n,skins:i,shortcodes:r,url:a,name:c,category:s,annotation:u})=>({unicode:n,name:c,shortcodes:r,url:a,category:s,annotation:u,id:n||c,skins:i&&o(i)}))}var st=requestAnimationFrame,Ln=typeof ResizeObserver=="function";function In(t,e,o){let n;Ln?(n=new ResizeObserver(i=>o(i[0].contentRect.width)),n.observe(t)):st(()=>o(t.getBoundingClientRect().width)),e.addEventListener("abort",()=>{n&&n.disconnect()})}function $o(t){{let e=document.createRange();return e.selectNode(t.firstChild),e.getBoundingClientRect().width}}var Mt;function Bn(t,e,o){for(let n of t){let i=o(n),r=$o(i);typeof Mt>"u"&&(Mt=$o(e));let a=r/1.8<Mt;Wt.set(n.unicode,a)}}function Rn(t){return zo(t,e=>e)}function _n(t){t&&(t.scrollTop=0)}function Ke(t,e,o){let n=t.get(e);return n||(n=o(),t.set(e,n)),n}function No(t){return""+t}function Mn(t){let e=document.createElement("template");return e.innerHTML=t,e}var $n=new WeakMap,Nn=new WeakMap,Fn=Symbol("un-keyed"),Wn="replaceChildren"in Element.prototype;function zn(t,e){Wn?t.replaceChildren(...e):(t.innerHTML="",t.append(...e))}function Un(t,e){let o=t.firstChild,n=0;for(;o;){if(e[n]!==o)return!0;o=o.nextSibling,n++}return n!==e.length}function Hn(t,e){let{targetNode:o}=e,{targetParentNode:n}=e,i=!1;n?i=Un(n,t):(i=!0,e.targetNode=void 0,e.targetParentNode=n=o.parentNode),i&&zn(n,t)}function Vn(t,e){for(let o of e){let{targetNode:n,currentExpression:i,binding:{expressionIndex:r,attributeName:a,attributeValuePre:c,attributeValuePost:s}}=o,u=t[r];if(i!==u)if(o.currentExpression=u,a)n.setAttribute(a,c+No(u)+s);else{let l;Array.isArray(u)?Hn(u,o):u instanceof Element?(l=u,n.replaceWith(l)):n.nodeValue=No(u),l&&(o.targetNode=l)}}}function Gn(t){let e="",o=!1,n=!1,i=-1,r=new Map,a=[];for(let s=0,u=t.length;s<u;s++){let l=t[s];if(e+=l,s===u-1)break;for(let v=0;v<l.length;v++)switch(l.charAt(v)){case"<":{l.charAt(v+1)==="/"?a.pop():(o=!0,a.push(++i));break}case">":{o=!1,n=!1;break}case"=":{n=!0;break}}let p=a[a.length-1],f=Ke(r,p,()=>[]),m,j,b;if(n){let v=/(\S+)="?([^"=]*)$/.exec(l);m=v[1],j=v[2],b=/^[^">]*/.exec(t[s+1])[0]}let g={attributeName:m,attributeValuePre:j,attributeValuePost:b,expressionIndex:s};f.push(g),!o&&!n&&(e+=" ")}return{template:Mn(e),elementsToBindings:r}}function Kn(t,e){let o=[],n=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT),i=t,r=-1;do{let a=e.get(++r);if(a)for(let c=0;c<a.length;c++){let s=a[c],u=s.attributeName?i:i.firstChild,l={binding:s,targetNode:u,targetParentNode:void 0,currentExpression:void 0};o.push(l)}}while(i=n.nextNode());return o}function qn(t){let{template:e,elementsToBindings:o}=Ke($n,t,()=>Gn(t)),n=e.cloneNode(!0).content.firstElementChild,i=Kn(n,o);return function(a){return Vn(a,i),n}}function Xn(t){let e=Ke(Nn,t,()=>new Map),o=Fn;function n(r,...a){let c=Ke(e,r,()=>new Map);return Ke(c,o,()=>qn(r))(a)}function i(r,a,c){return r.map((s,u)=>{let l=o;o=c(s);try{return a(s,u)}finally{o=l}})}return{map:i,html:n}}function Yn(t,e,o,n,i,r,a,c){let{labelWithSkin:s,titleForEmoji:u,unicodeWithSkin:l}=o,{html:p,map:f}=Xn(e);function m(g,v,k){return f(g,(x,w)=>p`<button role="${v?"option":"menuitem"}" aria-selected="${e.searchMode?w===e.activeSearchItem:""}" aria-label="${s(x,e.currentSkinTone)}" title="${u(x)}" class="emoji ${v&&w===e.activeSearchItem?"active":""}" id="${`${k}-${x.id}`}">${x.unicode?l(x,e.currentSkinTone):p`<img class="custom-emoji" src="${x.url}" alt="" loading="lazy">`}</button>`,x=>`${k}-${x.id}`)}let b=p`<section data-ref="rootElement" class="picker" aria-label="${e.i18n.regionLabel}" style="${e.pickerStyle}"><div class="pad-top"></div><div class="search-row"><div class="search-wrapper"><input id="search" class="search" type="search" role="combobox" enterkeyhint="search" placeholder="${e.i18n.searchLabel}" autocapitalize="none" autocomplete="off" spellcheck="true" aria-expanded="${!!(e.searchMode&&e.currentEmojis.length)}" aria-controls="search-results" aria-describedby="search-description" aria-autocomplete="list" aria-activedescendant="${e.activeSearchItemId?`emo-${e.activeSearchItemId}`:""}" data-ref="searchElement" data-on-input="onSearchInput" data-on-keydown="onSearchKeydown"><label class="sr-only" for="search">${e.i18n.searchLabel}</label> <span id="search-description" class="sr-only">${e.i18n.searchDescription}</span></div><div class="skintone-button-wrapper ${e.skinTonePickerExpandedAfterAnimation?"expanded":""}"><button id="skintone-button" class="emoji ${e.skinTonePickerExpanded?"hide-focus":""}" aria-label="${e.skinToneButtonLabel}" title="${e.skinToneButtonLabel}" aria-describedby="skintone-description" aria-haspopup="listbox" aria-expanded="${e.skinTonePickerExpanded}" aria-controls="skintone-list" data-on-click="onClickSkinToneButton">${e.skinToneButtonText}</button></div><span id="skintone-description" class="sr-only">${e.i18n.skinToneDescription}</span><div data-ref="skinToneDropdown" id="skintone-list" class="skintone-list hide-focus ${e.skinTonePickerExpanded?"":"hidden no-animate"}" style="transform:translateY(${e.skinTonePickerExpanded?0:"calc(-1 * var(--num-skintones) * var(--total-emoji-size))"})" role="listbox" aria-label="${e.i18n.skinTonesLabel}" aria-activedescendant="skintone-${e.activeSkinTone}" aria-hidden="${!e.skinTonePickerExpanded}" tabIndex="-1" data-on-focusout="onSkinToneOptionsFocusOut" data-on-click="onSkinToneOptionsClick" data-on-keydown="onSkinToneOptionsKeydown" data-on-keyup="onSkinToneOptionsKeyup">${f(e.skinTones,(g,v)=>p`<div id="skintone-${v}" class="emoji ${v===e.activeSkinTone?"active":""}" aria-selected="${v===e.activeSkinTone}" role="option" title="${e.i18n.skinTones[v]}" aria-label="${e.i18n.skinTones[v]}">${g}</div>`,g=>g)}</div></div><div class="nav" role="tablist" style="grid-template-columns:repeat(${e.groups.length},1fr)" aria-label="${e.i18n.categoriesLabel}" data-on-keydown="onNavKeydown" data-on-click="onNavClick">${f(e.groups,g=>p`<button role="tab" class="nav-button" aria-controls="tab-${g.id}" aria-label="${e.i18n.categories[g.name]}" aria-selected="${!e.searchMode&&e.currentGroup.id===g.id}" title="${e.i18n.categories[g.name]}" data-group-id="${g.id}"><div class="nav-emoji emoji">${g.emoji}</div></button>`,g=>g.id)}</div><div class="indicator-wrapper"><div class="indicator" style="transform:translateX(${(e.isRtl?-1:1)*e.currentGroupIndex*100}%)"></div></div><div class="message ${e.message?"":"gone"}" role="alert" aria-live="polite">${e.message}</div><div data-ref="tabpanelElement" class="tabpanel ${!e.databaseLoaded||e.message?"gone":""}" role="${e.searchMode?"region":"tabpanel"}" aria-label="${e.searchMode?e.i18n.searchResultsLabel:e.i18n.categories[e.currentGroup.name]}" id="${e.searchMode?"":`tab-${e.currentGroup.id}`}" tabIndex="0" data-on-click="onEmojiClick"><div data-action="calculateEmojiGridStyle">${f(e.currentEmojisWithCategories,(g,v)=>p`<div><div id="menu-label-${v}" class="category ${e.currentEmojisWithCategories.length===1&&e.currentEmojisWithCategories[0].category===""?"gone":""}" aria-hidden="true">${e.searchMode?e.i18n.searchResultsLabel:g.category?g.category:e.currentEmojisWithCategories.length>1?e.i18n.categories.custom:e.i18n.categories[e.currentGroup.name]}</div><div class="emoji-menu" role="${e.searchMode?"listbox":"menu"}" aria-labelledby="menu-label-${v}" id="${e.searchMode?"search-results":""}">${m(g.emojis,e.searchMode,"emo")}</div></div>`,g=>g.category)}</div></div><div class="favorites emoji-menu ${e.message?"gone":""}" role="menu" aria-label="${e.i18n.favoritesLabel}" style="padding-inline-end:${`${e.scrollbarWidth}px`}" data-on-click="onEmojiClick">${m(e.currentFavorites,!1,"fav")}</div><button data-ref="baselineEmoji" aria-hidden="true" tabindex="-1" class="abs-pos hidden emoji baseline-emoji">😀</button></section>`;if(c){t.appendChild(b);let g=(v,k)=>{for(let x of t.querySelectorAll(`[${v}]`))k(x,x.getAttribute(v))};for(let v of["click","focusout","input","keydown","keyup"])g(`data-on-${v}`,(k,x)=>{k.addEventListener(v,n[x])});g("data-ref",(v,k)=>{r[k]=v}),g("data-action",(v,k)=>{i[k](v)}),a.addEventListener("abort",()=>{t.removeChild(b)})}}var ct=typeof queueMicrotask=="function"?queueMicrotask:t=>Promise.resolve().then(t);function Zn(t){let e=!1,o,n=new Map,i=new Set,r,a=()=>{if(e)return;let u=[...i];i.clear();try{for(let l of u)l()}finally{r=!1,i.size&&(r=!0,ct(a))}},c=new Proxy({},{get(u,l){if(o){let p=n.get(l);p||(p=new Set,n.set(l,p)),p.add(o)}return u[l]},set(u,l,p){u[l]=p;let f=n.get(l);if(f){for(let m of f)i.add(m);r||(r=!0,ct(a))}return!0}}),s=u=>{let l=()=>{let p=o;o=l;try{return u()}finally{o=p}};return l()};return t.addEventListener("abort",()=>{e=!0}),{state:c,createEffect:s}}function $t(t,e,o){if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(!o(t[n],e[n]))return!1;return!0}var Nt=[],{assign:at}=Object;function Jn(t,e){let o={},n=new AbortController,i=n.signal,{state:r,createEffect:a}=Zn(i);at(r,{skinToneEmoji:void 0,i18n:void 0,database:void 0,customEmoji:void 0,customCategorySorting:void 0,emojiVersion:void 0}),at(r,e),at(r,{initialLoad:!0,currentEmojis:[],currentEmojisWithCategories:[],rawSearchText:"",searchText:"",searchMode:!1,activeSearchItem:-1,message:void 0,skinTonePickerExpanded:!1,skinTonePickerExpandedAfterAnimation:!1,currentSkinTone:0,activeSkinTone:0,skinToneButtonText:void 0,pickerStyle:void 0,skinToneButtonLabel:"",skinTones:[],currentFavorites:[],defaultFavoriteEmojis:void 0,numColumns:yn,isRtl:!1,scrollbarWidth:0,currentGroupIndex:0,groups:It,databaseLoaded:!1,activeSearchItemId:void 0}),a(()=>{r.currentGroup!==r.groups[r.currentGroupIndex]&&(r.currentGroup=r.groups[r.currentGroupIndex])});let c=d=>{t.getElementById(d).focus()},s=d=>t.getElementById(`emo-${d.id}`),u=(d,h)=>{o.rootElement.dispatchEvent(new CustomEvent(d,{detail:h,bubbles:!0,composed:!0}))},l=(d,h)=>d.id===h.id,p=(d,h)=>{let{category:y,emojis:E}=d,{category:D,emojis:L}=h;return y!==D?!1:$t(E,L,l)},f=d=>{$t(r.currentEmojis,d,l)||(r.currentEmojis=d)},m=d=>{r.searchMode!==d&&(r.searchMode=d)},j=d=>{$t(r.currentEmojisWithCategories,d,p)||(r.currentEmojisWithCategories=d)},b=(d,h)=>h&&d.skins&&d.skins[h]||d.unicode,k={labelWithSkin:(d,h)=>Rn([d.name||b(d,h),d.annotation,...d.shortcodes||Nt].filter(Boolean)).join(", "),titleForEmoji:d=>d.annotation||(d.shortcodes||Nt).join(", "),unicodeWithSkin:b},x={onClickSkinToneButton:$e,onEmojiClick:Oe,onNavClick:ie,onNavKeydown:H,onSearchKeydown:ne,onSkinToneOptionsClick:Ce,onSkinToneOptionsFocusOut:ue,onSkinToneOptionsKeydown:pe,onSkinToneOptionsKeyup:Ae,onSearchInput:oe},w={calculateEmojiGridStyle:O},T=!0;a(()=>{Yn(t,r,k,x,w,o,i,T),T=!1}),r.emojiVersion||Rt().then(d=>{d||(r.message=r.i18n.emojiUnsupportedMessage)}),a(()=>{async function d(){let h=!1,y=setTimeout(()=>{h=!0,r.message=r.i18n.loadingMessage},vn);try{await r.database.ready(),r.databaseLoaded=!0}catch(E){console.error(E),r.message=r.i18n.networkErrorMessage}finally{clearTimeout(y),h&&(h=!1,r.message="")}}r.database&&d()}),a(()=>{r.pickerStyle=`
      --num-groups: ${r.groups.length}; 
      --indicator-opacity: ${r.searchMode?0:1}; 
      --num-skintones: ${Ro};`}),a(()=>{r.customEmoji&&r.database&&S()}),a(()=>{r.customEmoji&&r.customEmoji.length?r.groups!==Ft&&(r.groups=Ft):r.groups!==It&&(r.currentGroupIndex&&r.currentGroupIndex--,r.groups=It)}),a(()=>{async function d(){r.databaseLoaded&&(r.currentSkinTone=await r.database.getPreferredSkinTone())}d()}),a(()=>{r.skinTones=Array(Ro).fill().map((d,h)=>Pn(r.skinToneEmoji,h))}),a(()=>{r.skinToneButtonText=r.skinTones[r.currentSkinTone]}),a(()=>{r.skinToneButtonLabel=r.i18n.skinToneLabel.replace("{skinTone}",r.i18n.skinTones[r.currentSkinTone])}),a(()=>{async function d(){let{database:h}=r,y=(await Promise.all(wn.map(E=>h.getEmojiByUnicodeOrName(E)))).filter(Boolean);r.defaultFavoriteEmojis=y}r.databaseLoaded&&d()});function S(){r.database.customEmoji=r.customEmoji||Nt}a(()=>{async function d(){S();let{database:h,defaultFavoriteEmojis:y,numColumns:E}=r,D=await h.getTopFavoriteEmoji(E),L=await R(zo([...D,...y],V=>V.unicode||V.name).slice(0,E));r.currentFavorites=L}r.databaseLoaded&&r.defaultFavoriteEmojis&&d()});function O(d){In(d,i,h=>{{let y=getComputedStyle(o.rootElement),E=parseInt(y.getPropertyValue("--num-columns"),10),D=y.getPropertyValue("direction")==="rtl",V=d.parentElement.getBoundingClientRect().width-h;r.numColumns=E,r.scrollbarWidth=V,r.isRtl=D}})}a(()=>{async function d(){let{searchText:h,currentGroup:y,databaseLoaded:E,customEmoji:D}=r;if(!E)r.currentEmojis=[],r.searchMode=!1;else if(h.length>=hn){let L=await z(h);r.searchText===h&&(f(L),m(!0))}else{let{id:L}=y;if(L!==-1||D&&D.length){let V=await q(L);r.currentGroup.id===L&&(f(V),m(!1))}}}d()}),a(()=>{let{currentEmojis:d,emojiVersion:h}=r,y=d.filter(E=>E.unicode).filter(E=>_o(E)&&!Wt.has(E.unicode));if(!h&&y.length)f(d),st(()=>M(y));else{let E=h?d:d.filter(B);f(E),st(()=>_n(o.tabpanelElement))}});function M(d){Bn(d,o.baselineEmoji,s),r.currentEmojis=r.currentEmojis}function B(d){return!d.unicode||!_o(d)||Wt.get(d.unicode)}async function A(d){let h=r.emojiVersion||await Rt();return d.filter(({version:y})=>!y||y<=h)}async function R(d){return Dn(d,r.emojiVersion||await Rt())}async function q(d){let h=d===-1?r.customEmoji:await r.database.getEmojiByGroup(d);return R(await A(h))}async function z(d){return R(await A(await r.database.getEmojiBySearchQuery(d)))}a(()=>{}),a(()=>{function d(){let{searchMode:y,currentEmojis:E}=r;if(y)return[{category:"",emojis:E}];let D=new Map;for(let L of E){let V=L.category||"",me=D.get(V);me||(me=[],D.set(V,me)),me.push(L)}return[...D.entries()].map(([L,V])=>({category:L,emojis:V})).sort((L,V)=>r.customCategorySorting(L.category,V.category))}let h=d();j(h)}),a(()=>{r.activeSearchItemId=r.activeSearchItem!==-1&&r.currentEmojis[r.activeSearchItem].id}),a(()=>{let{rawSearchText:d}=r;Fo(()=>{r.searchText=(d||"").trim(),r.activeSearchItem=-1})});function ne(d){if(!r.searchMode||!r.currentEmojis.length)return;let h=y=>{re(d),r.activeSearchItem=_t(y,r.activeSearchItem,r.currentEmojis)};switch(d.key){case"ArrowDown":return h(!1);case"ArrowUp":return h(!0);case"Enter":if(r.activeSearchItem===-1)r.activeSearchItem=0;else return re(d),K(r.currentEmojis[r.activeSearchItem].id)}}function ie(d){let{target:h}=d,y=h.closest(".nav-button");if(!y)return;let E=parseInt(y.dataset.groupId,10);o.searchElement.value="",r.rawSearchText="",r.searchText="",r.activeSearchItem=-1,r.currentGroupIndex=r.groups.findIndex(D=>D.id===E)}function H(d){let{target:h,key:y}=d,E=D=>{D&&(re(d),D.focus())};switch(y){case"ArrowLeft":return E(h.previousElementSibling);case"ArrowRight":return E(h.nextElementSibling);case"Home":return E(h.parentElement.firstElementChild);case"End":return E(h.parentElement.lastElementChild)}}async function K(d){let h=await r.database.getEmojiByUnicodeOrName(d),y=[...r.currentEmojis,...r.currentFavorites].find(D=>D.id===d),E=y.unicode&&b(y,r.currentSkinTone);await r.database.incrementFavoriteEmojiCount(d),u("emoji-click",{emoji:h,skinTone:r.currentSkinTone,...E&&{unicode:E},...y.name&&{name:y.name}})}async function Oe(d){let{target:h}=d;if(!h.classList.contains("emoji"))return;re(d);let y=h.id.substring(4);K(y)}function J(d){r.currentSkinTone=d,r.skinTonePickerExpanded=!1,c("skintone-button"),u("skin-tone-change",{skinTone:d}),r.database.setPreferredSkinTone(d)}function Ce(d){let{target:{id:h}}=d,y=h&&h.match(/^skintone-(\d)/);if(!y)return;re(d);let E=parseInt(y[1],10);J(E)}function $e(d){r.skinTonePickerExpanded=!r.skinTonePickerExpanded,r.activeSkinTone=r.currentSkinTone,r.skinTonePickerExpanded&&(re(d),st(()=>c("skintone-list")))}a(()=>{r.skinTonePickerExpanded?o.skinToneDropdown.addEventListener("transitionend",()=>{r.skinTonePickerExpandedAfterAnimation=!0},{once:!0}):r.skinTonePickerExpandedAfterAnimation=!1});function pe(d){if(!r.skinTonePickerExpanded)return;let h=async y=>{re(d),r.activeSkinTone=y};switch(d.key){case"ArrowUp":return h(_t(!0,r.activeSkinTone,r.skinTones));case"ArrowDown":return h(_t(!1,r.activeSkinTone,r.skinTones));case"Home":return h(0);case"End":return h(r.skinTones.length-1);case"Enter":return re(d),J(r.activeSkinTone);case"Escape":return re(d),r.skinTonePickerExpanded=!1,c("skintone-button")}}function Ae(d){if(r.skinTonePickerExpanded)switch(d.key){case" ":return re(d),J(r.activeSkinTone)}}async function ue(d){let{relatedTarget:h}=d;(!h||h.id!=="skintone-list")&&(r.skinTonePickerExpanded=!1)}function oe(d){r.rawSearchText=d.target.value}return{$set(d){at(r,d)},$destroy(){n.abort()}}}var Qn="https://cdn.jsdelivr.net/npm/emoji-picker-element-data@^1/en/emojibase/data.json",ei="en",ti={categoriesLabel:"Categories",emojiUnsupportedMessage:"Your browser does not support color emoji.",favoritesLabel:"Favorites",loadingMessage:"Loading\u2026",networkErrorMessage:"Could not load emoji.",regionLabel:"Emoji picker",searchDescription:"When search results are available, press up or down to select and enter to choose.",searchLabel:"Search",searchResultsLabel:"Search results",skinToneDescription:"When expanded, press up or down to select and enter to choose.",skinToneLabel:"Choose a skin tone (currently {skinTone})",skinTonesLabel:"Skin tones",skinTones:["Default","Light","Medium-Light","Medium","Medium-Dark","Dark"],categories:{custom:"Custom","smileys-emotion":"Smileys and emoticons","people-body":"People and body","animals-nature":"Animals and nature","food-drink":"Food and drink","travel-places":"Travel and places",activities:"Activities",objects:"Objects",symbols:"Symbols",flags:"Flags"}},oi=":host{--emoji-size:1.375rem;--emoji-padding:0.5rem;--category-emoji-size:var(--emoji-size);--category-emoji-padding:var(--emoji-padding);--indicator-height:3px;--input-border-radius:0.5rem;--input-border-size:1px;--input-font-size:1rem;--input-line-height:1.5;--input-padding:0.25rem;--num-columns:8;--outline-size:2px;--border-size:1px;--skintone-border-radius:1rem;--category-font-size:1rem;display:flex;width:min-content;height:400px}:host,:host(.light){color-scheme:light;--background:#fff;--border-color:#e0e0e0;--indicator-color:#385ac1;--input-border-color:#999;--input-font-color:#111;--input-placeholder-color:#999;--outline-color:#999;--category-font-color:#111;--button-active-background:#e6e6e6;--button-hover-background:#d9d9d9}:host(.dark){color-scheme:dark;--background:#222;--border-color:#444;--indicator-color:#5373ec;--input-border-color:#ccc;--input-font-color:#efefef;--input-placeholder-color:#ccc;--outline-color:#fff;--category-font-color:#efefef;--button-active-background:#555555;--button-hover-background:#484848}@media (prefers-color-scheme:dark){:host{color-scheme:dark;--background:#222;--border-color:#444;--indicator-color:#5373ec;--input-border-color:#ccc;--input-font-color:#efefef;--input-placeholder-color:#ccc;--outline-color:#fff;--category-font-color:#efefef;--button-active-background:#555555;--button-hover-background:#484848}}:host([hidden]){display:none}button{margin:0;padding:0;border:0;background:0 0;box-shadow:none;-webkit-tap-highlight-color:transparent}button::-moz-focus-inner{border:0}input{padding:0;margin:0;line-height:1.15;font-family:inherit}input[type=search]{-webkit-appearance:none}:focus{outline:var(--outline-color) solid var(--outline-size);outline-offset:calc(-1*var(--outline-size))}:host([data-js-focus-visible]) :focus:not([data-focus-visible-added]){outline:0}:focus:not(:focus-visible){outline:0}.hide-focus{outline:0}*{box-sizing:border-box}.picker{contain:content;display:flex;flex-direction:column;background:var(--background);border:var(--border-size) solid var(--border-color);width:100%;height:100%;overflow:hidden;--total-emoji-size:calc(var(--emoji-size) + (2 * var(--emoji-padding)));--total-category-emoji-size:calc(var(--category-emoji-size) + (2 * var(--category-emoji-padding)))}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);border:0}.hidden{opacity:0;pointer-events:none}.abs-pos{position:absolute;left:0;top:0}.gone{display:none!important}.skintone-button-wrapper,.skintone-list{background:var(--background);z-index:3}.skintone-button-wrapper.expanded{z-index:1}.skintone-list{position:absolute;inset-inline-end:0;top:0;z-index:2;overflow:visible;border-bottom:var(--border-size) solid var(--border-color);border-radius:0 0 var(--skintone-border-radius) var(--skintone-border-radius);will-change:transform;transition:transform .2s ease-in-out;transform-origin:center 0}@media (prefers-reduced-motion:reduce){.skintone-list{transition-duration:.001s}}@supports not (inset-inline-end:0){.skintone-list{right:0}}.skintone-list.no-animate{transition:none}.tabpanel{overflow-y:auto;-webkit-overflow-scrolling:touch;will-change:transform;min-height:0;flex:1;contain:content}.emoji-menu{display:grid;grid-template-columns:repeat(var(--num-columns),var(--total-emoji-size));justify-content:space-around;align-items:flex-start;width:100%}.category{padding:var(--emoji-padding);font-size:var(--category-font-size);color:var(--category-font-color)}.custom-emoji,.emoji,button.emoji{height:var(--total-emoji-size);width:var(--total-emoji-size)}.emoji,button.emoji{font-size:var(--emoji-size);display:flex;align-items:center;justify-content:center;border-radius:100%;line-height:1;overflow:hidden;font-family:var(--emoji-font-family);cursor:pointer}@media (hover:hover) and (pointer:fine){.emoji:hover,button.emoji:hover{background:var(--button-hover-background)}}.emoji.active,.emoji:active,button.emoji.active,button.emoji:active{background:var(--button-active-background)}.custom-emoji{padding:var(--emoji-padding);object-fit:contain;pointer-events:none;background-repeat:no-repeat;background-position:center center;background-size:var(--emoji-size) var(--emoji-size)}.nav,.nav-button{align-items:center}.nav{display:grid;justify-content:space-between;contain:content}.nav-button{display:flex;justify-content:center}.nav-emoji{font-size:var(--category-emoji-size);width:var(--total-category-emoji-size);height:var(--total-category-emoji-size)}.indicator-wrapper{display:flex;border-bottom:1px solid var(--border-color)}.indicator{width:calc(100%/var(--num-groups));height:var(--indicator-height);opacity:var(--indicator-opacity);background-color:var(--indicator-color);will-change:transform,opacity;transition:opacity .1s linear,transform .25s ease-in-out}@media (prefers-reduced-motion:reduce){.indicator{will-change:opacity;transition:opacity .1s linear}}.pad-top,input.search{background:var(--background);width:100%}.pad-top{height:var(--emoji-padding);z-index:3}.search-row{display:flex;align-items:center;position:relative;padding-inline-start:var(--emoji-padding);padding-bottom:var(--emoji-padding)}.search-wrapper{flex:1;min-width:0}input.search{padding:var(--input-padding);border-radius:var(--input-border-radius);border:var(--input-border-size) solid var(--input-border-color);color:var(--input-font-color);font-size:var(--input-font-size);line-height:var(--input-line-height)}input.search::placeholder{color:var(--input-placeholder-color)}.favorites{display:flex;flex-direction:row;border-top:var(--border-size) solid var(--border-color);contain:content}.message{padding:var(--emoji-padding)}",Uo=["customEmoji","customCategorySorting","database","dataSource","i18n","locale","skinToneEmoji","emojiVersion"],ri=`:host{--emoji-font-family:${Wo}}`,Se=class extends HTMLElement{constructor(e){super(),this.attachShadow({mode:"open"});let o=document.createElement("style");o.textContent=oi+ri,this.shadowRoot.appendChild(o),this._ctx={locale:ei,dataSource:Qn,skinToneEmoji:bn,customCategorySorting:xn,customEmoji:null,i18n:ti,emojiVersion:null,...e};for(let n of Uo)n!=="database"&&Object.prototype.hasOwnProperty.call(this,n)&&(this._ctx[n]=this[n],delete this[n]);this._dbFlush()}connectedCallback(){this._cmp||(this._cmp=Jn(this.shadowRoot,this._ctx))}disconnectedCallback(){ct(()=>{if(!this.isConnected&&this._cmp){this._cmp.$destroy(),this._cmp=void 0;let{database:e}=this._ctx;e.close().catch(o=>console.error(o))}})}static get observedAttributes(){return["locale","data-source","skin-tone-emoji","emoji-version"]}attributeChangedCallback(e,o,n){this._set(e.replace(/-([a-z])/g,(i,r)=>r.toUpperCase()),e==="emoji-version"?parseFloat(n):n)}_set(e,o){this._ctx[e]=o,this._cmp&&this._cmp.$set({[e]:o}),["locale","dataSource"].includes(e)&&this._dbFlush()}_dbCreate(){let{locale:e,dataSource:o,database:n}=this._ctx;(!n||n.locale!==e||n.dataSource!==o)&&this._set("database",new Ge({locale:e,dataSource:o}))}_dbFlush(){ct(()=>this._dbCreate())}},Ho={};for(let t of Uo)Ho[t]={get(){return t==="database"&&this._dbCreate(),this._ctx[t]},set(e){if(t==="database")throw new Error("database is read-only");this._set(t,e)}};Object.defineProperties(Se.prototype,Ho);customElements.get("emoji-picker")||customElements.define("emoji-picker",Se);function Vo(){return document.documentElement.classList.contains("dark")}async function ni(t){let e=t.detail.element,o=e.querySelector(".emoji-picker-button"),n=e.querySelector(".emoji-picker-popup"),i=n.querySelector("emoji-picker");if(i!=null){i.classList.remove("dark","light"),i.classList.add(Vo()?"dark":"light");return}let r=await import(window.filamentData.emojiPicker.i18n),a=new Se({i18n:r.default,locale:window.filamentData.emojiPicker.locale,dataSource:window.filamentData.emojiPicker.datasource});a.classList.add(Vo()?"dark":"light"),n.appendChild(a),kt(o,n,{placement:n.dataset.popupPlacement,modifiers:[{name:"offset",options:{offset:[parseInt(n.dataset.popupOffsetX),parseInt(n.dataset.popupOffsetY)]}}]})}document.addEventListener("emoji-picker-toggle",ni);})();
