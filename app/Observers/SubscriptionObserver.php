<?php

namespace App\Observers;

use App\Enums\Subscriptions;
use App\Models\Subscription;
use App\Models\SubscriptionHistory;
use App\Traits\Blamable;

class SubscriptionObserver
{
    use Blamable;

    public static bool $skipObserver = false;

//    'new','renewal','suspension','termination','assigned_quota_change','remaining_quota_change','payment_update',
//    'payment_approved','payment_rejected','license_key','activation','auto_renew_setting_change','extension'
    public static ?string $eventType = null;
    
    public static string|array|null $eventDescription = null;
    public static ?string $rejectionReasonDescription = null;
    public static ?string $rejectionReason = null;

    /**
     * Handle the Subscription "created" event.
     */
    public function created(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $this->setDescriptionTranslations($history);
        $history->save();
    }

    /**
     * Handle the Subscription "updated" event.
     */
    public function updated(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        if (!$subscription->isDirty()) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);

        // Auto-detect event type if not explicitly set
        if (self::$eventType === null) {
            $history->history_event = $this->detectEventType($subscription);
        } else {
            $history->history_event = self::$eventType;
        }

        $history->rejection_reason_description = self::$rejectionReasonDescription;
        $history->rejection_reason = self::$rejectionReason;
        $this->setDescriptionTranslations($history);
        $history->save();

        // Reset event type after use to prevent it from affecting later updates
        self::$eventType = null;
    }

    /**
     * Handle the Subscription "deleted" event.
     */
    public function deleted(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $this->setDescriptionTranslations($history);
        $history->deleted_at = now();
        $history->deleted_by = self::getBlamableId();
        $history->save();
    }

    /**
     * Handle the Subscription "restored" event.
     */
    public function restored(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $this->setDescriptionTranslations($history);
        $history->save();
    }

    /**
     * Handle the Subscription "force deleted" event.
     */
    public function forceDeleted(Subscription $subscription): void
    {
        if (self::$skipObserver) {
            return;
        }

        $modelData = SubscriptionHistory::mapParentColumns($subscription->toArray());

        $history = new SubscriptionHistory($modelData);
        $history->history_event = self::$eventType;
        $this->setDescriptionTranslations($history);
        $history->save();
    }

    /**
     * Automatically detect the event type based on changed fields.
     */
    private function detectEventType(Subscription $subscription): ?string
    {
        $dirtyFields = array_keys($subscription->getDirty());

        // Check for specific field changes in order of priority
        if (in_array('assigned_quota', $dirtyFields)) {
            return Subscriptions::HISTORY_EVENT_TYPE_ASSIGNED_QUOTA_CHANGE->value;
        }

        if (in_array('remaining_quota', $dirtyFields)) {
            return Subscriptions::HISTORY_EVENT_TYPE_REMAINING_QUOTA_CHANGE->value;
        }

        if (in_array('auto_renew', $dirtyFields)) {
            return Subscriptions::HISTORY_EVENT_TYPE_AUTO_RENEW_SETTING_CHANGE->value;
        }

        if (in_array('subscription_status', $dirtyFields)) {
            $newStatus = $subscription->subscription_status;
            return match ($newStatus) {
                Subscriptions::SUBSCRIPTION_STATUS_SUSPENDED->value => Subscriptions::HISTORY_EVENT_TYPE_SUSPENSION->value,
                Subscriptions::SUBSCRIPTION_STATUS_TERMINATED->value => Subscriptions::HISTORY_EVENT_TYPE_TERMINATION->value,
                Subscriptions::SUBSCRIPTION_STATUS_EXPIRED->value => Subscriptions::HISTORY_EVENT_TYPE_EXPIRATION->value,
                Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value => Subscriptions::HISTORY_EVENT_TYPE_ACTIVATION->value,
                default => Subscriptions::HISTORY_EVENT_TYPE_SUBSCRIPTION_UPDATE->value
            };
        }

        if (in_array('payment_status', $dirtyFields)) {
            $newPaymentStatus = $subscription->payment_status;
            return match ($newPaymentStatus) {
                Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PAID->value => Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_APPROVED->value,
                Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_FAILED->value => Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_REJECTED->value,
                default => Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_UPDATE->value
            };
        }

        if (in_array('ends_at', $dirtyFields)) {
            return Subscriptions::HISTORY_EVENT_TYPE_EXTENSION->value;
        }

        // Default fallback for general subscription updates
        return Subscriptions::HISTORY_EVENT_TYPE_SUBSCRIPTION_UPDATE->value;
    }

    /**
     * Set description translations on the history model.
     */
    private function setDescriptionTranslations(SubscriptionHistory $history): void
    {
        $locale = app()->getLocale();
        $fallbackLocale = config('app.fallback_locale', 'en');

        // If description is an array, set translations for each locale
        if (is_array(self::$eventDescription)) {
            foreach (self::$eventDescription as $lang => $text) {
                $history->setTranslation('description', $lang, $text);
            }
            return;
        }

        // If description is a string, set it for current locale and fallback
        if (is_string(self::$eventDescription)) {
            // Set for current locale
            $history->setTranslation('description', $locale, self::$eventDescription);

            // Set for fallback locale if different
            if ($locale !== $fallbackLocale) {
                $history->setTranslation('description', $fallbackLocale, self::$eventDescription);
            }
        }
    }
}
