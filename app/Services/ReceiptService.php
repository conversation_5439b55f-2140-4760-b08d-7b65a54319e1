<?php

namespace App\Services;

use App\Enums\SubscriptionLicenses;
use App\Enums\SubscriptionReceipts;
use App\Helpers\AppHelper;
use App\Jobs\ProvisionClientResourcesJob;
use App\Mail\PaymentApprovedMail;
use App\Mail\PaymentRejectedMail;
use App\Enums\Notifications;
use App\Enums\Subscriptions;
use App\Models\Subscription;
use App\Observers\SubscriptionLicenseObserver;
use App\Observers\SubscriptionObserver;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;

class ReceiptService
{
    public static function approve($receipt, array $data = []): bool
    {
        try {
            $receipt->receipt_status = SubscriptionReceipts::RECEIPT_STATUS_APPROVED->value;
            $receipt->approved_by = auth()->id();
            $receipt->paid_at = now();
            $receipt->comment = $data['comment'] ?? null;
            $receipt->save();

            $subscription = $receipt->subscription;

            $nextStatus = $subscription->subscriptionHasFeaturePrefix(Subscription::SUBSCRIPTION_TYPE_ON_PREM)
                ? Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value
                : Subscriptions::SUBSCRIPTION_STATUS_PENDING_LICENSE_KEY->value;

            SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_APPROVED->value;

            $updateData = [
                'subscription_status' => $nextStatus,
                'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PAID->value,
                'last_payment_date' => now(),
            ];

            // Set start and end dates for on-prem subscriptions when they become active
            if ($nextStatus === Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value &&
                $subscription->subscriptionHasFeaturePrefix(Subscription::SUBSCRIPTION_TYPE_ON_PREM)) {

                if (empty($subscription->starts_at)) {
                    $updateData['starts_at'] = now();
                }

                if (empty($subscription->ends_at)) {
                    $updateData['ends_at'] = now()->addYears($subscription->number_of_years());
                }
            }

            $subscription->update($updateData);

            $subscriber = $subscription->subscriber;

            if ($subscriber && $subscriber?->primaryContact) {
                Mail::to($subscriber?->primaryContact?->email)
                    ->queue(new PaymentApprovedMail($subscription));
            }

            if ($nextStatus === Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value) {
                SubscriptionService::sendSubscriptionActivatedEmail($subscription);
            }

            $notificationService = app(NotificationService::class);

            if ($subscriber && $subscriber?->primaryContact) {
                $notificationService->createNotification(
                    $subscriber?->primaryContact,
                    Notifications::NOTIFICATION_EVENT_RECEIPT_APPROVED->value,
                    [
                        'user_name' => $subscriber?->primaryContact?->name,
                        'subscription_id' => $subscription->public_subscription_id
                    ]
                );
            }

            // If Saas: create license and dispatch provision job
            if ($subscription->isSaas()) {
                SubscriptionLicenseObserver::$skipObserver = true;

                $clientNameIdentifier = "{$subscriber->name} {$subscription->id}";
                $sonarUrl = Str::slug($clientNameIdentifier) . '.' . config('services.cloudflare.base_domain');
                $normalizedUrl = "https://" . rtrim(AppHelper::normalizeDomain($sonarUrl), '/');

                $domainAlreadyExists = $subscriber->companyLicenses()
                    ->where('sonar_url', $normalizedUrl)
                    ->exists();

                $shouldNormalizeDomain = !((bool) $subscriber->hasPreviousEnvironment()) && !$domainAlreadyExists;

                Log::info("domainAlreadyExists {$domainAlreadyExists} AND HasPevEnv {$shouldNormalizeDomain} AND Sonar URL: {$normalizedUrl}");

                $sonarUrl = $shouldNormalizeDomain ? $normalizedUrl : "https://" . rtrim($sonarUrl, '/');

                Log::info("Provisioning resources for client: {$clientNameIdentifier} with Sonar URL: {$sonarUrl}");

                $subscription->subscriptionLicenses()->create([
                    'company_id' => $subscriber->company_id,
                    'license_type' => Subscriptions::LICENSE_TYPE_COMMERCIAL->value,
                    'server_id' => null,
                    'environment' => Subscriptions::ENVIRONMENT_OPTION_PRODUCTION->value,
                    'sonar_url' => $sonarUrl,
                    'sonar_username' => config('settings.sonarqube.default_username'),
                    'sonar_password' => Crypt::encryptString(config('settings.sonarqube.default_password')),
                    'sonar_api_token' => null,
                    'environment_status' => SubscriptionLicenses::ENVIRONMENT_STATUS_PENDING->value,
                ]);

                ProvisionClientResourcesJob::dispatch($clientNameIdentifier, $shouldNormalizeDomain);

                SubscriptionLicenseObserver::$skipObserver = false;
            }

            Notification::make()
                ->title('Receipt Approved')
                ->body('The receipt has been approved and the subscription status has been updated.')
                ->success()
                ->send();

            return true;
        } catch (\Throwable $e) {
            Log::error('Receipt approval failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            Notification::make()
                ->title('Approval Failed')
                ->body('An unexpected error occurred while approving the receipt. Please check logs.')
                ->danger()
                ->send();

            return false;
        }
    }

    public static function reject($receipt, array $data): bool
    {
        try {
            $receipt->receipt_status = SubscriptionReceipts::RECEIPT_STATUS_REJECTED->value;
            $receipt->rejected_by = auth()->id();
            $receipt->rejection_reason = $data['rejection_reason'];
            $receipt->comment = $data['comment'] ?? null;
            $receipt->save();

            $subscription = $receipt->subscription;

            // Observer hooks
            SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_REJECTED->value;
            SubscriptionObserver::$rejectionReason = $data['rejection_reason'];
            SubscriptionObserver::$rejectionReasonDescription = $data['comment'] ?? null;

            // Refresh and update subscription
            $subscription = $subscription->fresh();
            $subscription->subscription_status = Subscriptions::SUBSCRIPTION_STATUS_PENDING_RECEIPT->value;
            $subscription->payment_status = Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_FAILED->value;
            $subscription->save();

            $subscriber = $subscription->subscriber;

            // Send email
            if ($subscriber && $subscriber?->primaryContact) {
                Mail::to($subscriber?->primaryContact?->email)
                    ->queue(new PaymentRejectedMail(
                        $subscription,
                        $data['rejection_reason'],
                        $data['comment'] ?? null
                    ));
            }

            // Notification service
            $notificationService = app(NotificationService::class);
            if ($subscriber && $subscriber?->primaryContact) {
                $notificationService->createNotification(
                    $subscriber?->primaryContact,
                    Notifications::NOTIFICATION_EVENT_RECEIPT_REJECTED->value,
                    [
                        'user_name' => $subscriber?->primaryContact?->name,
                        'subscription_id' => $subscription->public_subscription_id
                    ]
                );
            }

            Notification::make()
                ->title('Receipt Rejected')
                ->body('The receipt has been rejected and the subscription status has been updated.')
                ->warning()
                ->send();

            return true;
        } catch (\Throwable $e) {
            Log::error('Receipt rejection failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            Notification::make()
                ->title('Rejection Failed')
                ->body('An unexpected error occurred while rejecting the receipt. Please check logs.')
                ->danger()
                ->send();

            return false;
        }
    }

}
