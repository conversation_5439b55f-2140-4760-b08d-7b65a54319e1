<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Services\GcsService;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class SubscriptionReceipt extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable, Timezones;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'subscription_receipts_id';

    /**
     * The table associated with the model.
     *
     * @var string|null
     */
    protected $table = 'subscription_receipts';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
    */
    protected $fillable = [
        'subscription_id',
        'receipt_number',
        'receipt_path',
        'storage',
        'amount',
        'currency_code',
        'receipt_status',
        'approved_by',
        'rejected_by',
        'rejection_reason',
        'comment',
        'paid_at',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'paid_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            //'password' => 'hashed',
            //'countries' => 'array',
        ];
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id', 'id');
    }

    public function getReceiptUrlAttribute()
    {
        return app(GcsService::class)->setFolder('receipts')->setFilePath($this->receipt_path)->getFile();
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });
        
        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}