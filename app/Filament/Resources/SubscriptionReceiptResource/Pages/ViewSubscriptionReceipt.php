<?php

namespace App\Filament\Resources\SubscriptionReceiptResource\Pages;

use App\Enums\PaymentRejectionReason;
use App\Enums\Subscriptions;
use App\Filament\Resources\SubscriptionReceiptResource;
use App\Helpers\SubscriptionHelper;
use App\Models\User;
use App\Services\MoneyFormatter;
use App\Services\ReceiptService;
use Cknow\Money\Money;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;

class ViewSubscriptionReceipt extends ViewRecord
{
    protected static string $resource = SubscriptionReceiptResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('approve')
                ->label('Approve Receipt')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn() => $this->record->receipt_status === Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value && auth()->user()->can('update_subscription::receipt'))
                ->form([
                    Forms\Components\Textarea::make('comment')
                        ->label('Comment (Optional)')
                        ->placeholder('Add any additional notes about the approval')
                        ->maxLength(1000)
                ])
                ->modalHeading('Approve Receipt')
                ->modalDescription('Are you sure you want to approve this receipt? This will update the subscription status.')
                ->modalSubmitActionLabel('Yes, approve receipt')
                ->action(function (array $data) {
                    if (ReceiptService::approve($this->record, $data)) {
                        $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                    }
                }),
            
            Actions\Action::make('reject')
                ->label('Reject Receipt')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(fn() => $this->record->receipt_status === Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value && auth()->user()->can('update_subscription::receipt'))
                ->form([
                    Forms\Components\Select::make('rejection_reason')
                        ->label('Rejection Reason')
                        ->options(PaymentRejectionReason::getValues())
                        ->required()
                        ->searchable(),
                    Forms\Components\Textarea::make('comment')
                        ->label('Additional Details')
                        ->placeholder('Provide additional details about the rejection reason')
                        ->maxLength(1000)
                ])
                ->action(function (array $data) {
                    if (ReceiptService::reject($this->record, $data)) {
                        $this->redirect($this->getResource()::getUrl('view', ['record' => $this->record]));
                    }
                })
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Receipt Information')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('receipt_number')
                                    ->label('Receipt Number'),
                                
                                Infolists\Components\TextEntry::make('amount')
                                    ->label('Receipt Amount')
                                    ->formatStateUsing(function ($state) {
                                        $priceModel = new Money($state, $this->record->currency_code);
                                        return app(MoneyFormatter::class)->price($priceModel->divide(100)->getAmount(), $priceModel->getCurrency())->format();
                                    }),

                                TextEntry::make('receipt_status')
                                    ->label('Receipt Status')
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        'approved' => 'success',
                                        'rejected' => 'danger',
                                        default => 'warning',
                                    }),

                                Infolists\Components\ViewEntry::make('receipt_preview')
                                    ->label('Receipt File')
                                    ->view('filament.components.receipt-preview'),

                                TextEntry::make('created_at')
                                    ->label('Uploaded Date & Time')
                                    ->dateTime(),

                                TextEntry::make('paid_at')
                                    ->label('Paid At')
                                    ->dateTime()
                                    ->placeholder('Not paid yet'),
                                
                                Infolists\Components\TextEntry::make('approved_by')
                                    ->label('Approved By')
                                    ->visible(function ($record) {
                                        return $record->approved_by;
                                    })
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return 'Not approved yet';
                                        $user = User::find($state);
                                        return $user ? $user->name : $state;
                                    }),

                                Infolists\Components\TextEntry::make('rejected_by')
                                    ->label('Rejected By')
                                    ->visible(function ($record) {
                                        return $record->rejected_by;
                                    })
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return 'Not rejected';
                                        $user = User::find($state);
                                        return $user ? $user->name : $state;
                                    }),

                                TextEntry::make('comment')
                                    ->label('Comment')
                                    ->visible(function ($record) {
                                        return !empty($record->comment);
                                    })
                                    ->placeholder('No comment provided'),

                                TextEntry::make('updated_at')
                                    ->label(function ($record) {
                                        if ($record->approved_by) return 'Approved At';
                                        if ($record->rejected_by) return 'Rejected At';
                                        return 'Approval Timestamp';
                                    })
                                    ->visible(function ($record) {
                                        return $record->approved_by || $record->rejected_by;
                                    })
                                    ->dateTime()
                                    ->placeholder('Not approved or rejected yet'),

//                                TextEntry::make('approved_at')
//                                    ->label('Approved At')
//                                    ->dateTime()
//                                    ->placeholder('Not approved yet'),
//
//                                TextEntry::make('rejected_at')
//                                    ->label('Rejected At')
//                                    ->dateTime()
//                                    ->placeholder('Not rejected yet'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Subscription Details')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                TextEntry::make('product_name')
                                    ->label('Product Name')
                                    ->default('Sonar')
                                    ->badge()
                                    ->color('primary'),

                                TextEntry::make('subscription.public_subscription_id')
                                    ->label('Subscription ID')
                                    ->url(fn () => $this->record->subscription ?
                                        route('filament.admin.resources.subscriptions.view', ['record' => $this->record->subscription]) : null)
                                    ->openUrlInNewTab(),

                                TextEntry::make('subscription.plan.name')
                                    ->label('Plan Name'),

                                TextEntry::make('subscription.plan_id')
                                    ->label('Subscription Type')
                                    ->formatStateUsing(fn ($record) => $record->subscription?->getReadableType() ?? 'Unknown'),

                                TextEntry::make('subscription.plan_features_display')
                                    ->label('Plan Features')
                                    ->getStateUsing(function () {
                                        $subscription = $this->record->subscription;
                                        if (!$subscription || !$subscription->usage) return '-';

                                        $features = [];
                                        foreach ($subscription->usage as $usage) {
                                            $planFeature = $usage->planFeature;
                                            if (!$planFeature || !$planFeature->feature || $planFeature->is_addon) {
                                                continue; // Skip if missing or is an addon
                                            }

                                            $minValue = $planFeature->valid_from_minimum ? " (Min: {$planFeature->valid_from_minimum})" : '';
                                            $maxValue = $planFeature->valid_to_maximum ? " (Max: {$planFeature->valid_to_maximum})" : '';
                                            $price = $planFeature->price ? " - Price: " . MoneyFormatter::price(($planFeature->price * 100), 'USD')->format() : '';
                                            $planFeatureName = SubscriptionHelper::getPlanFeatureName($planFeature->feature->name, $planFeature->feature->slug);

                                            $features[] = "{$planFeatureName}{$minValue}{$maxValue}{$price}";
                                        }

                                        return implode('<br>', $features) ?: 'No features';
                                    })
                                    ->html(),

                                TextEntry::make('subscription.addon_features_display')
                                    ->label('Add-ons')
                                    ->getStateUsing(function () {
                                        $subscription = $this->record->subscription;
                                        if (!$subscription || !$subscription->usage) return '-';

                                        $addons = [];
                                        foreach ($subscription->usage as $usage) {
                                            $planFeature = $usage->planFeature;
                                            if (!$planFeature || !$planFeature->feature || !$planFeature->is_addon) {
                                                continue; // Skip if missing or not an addon
                                            }

                                            $price = $planFeature->price ? " - Price: " . MoneyFormatter::price(($planFeature->price * 100), 'USD')->format() : '';
                                            $planFeatureName = SubscriptionHelper::getPlanFeatureName($planFeature->feature->name, $planFeature->feature->slug);

                                            $addons[] = "{$planFeatureName}{$price}";
                                        }

                                        return implode('<br>', $addons) ?: 'No add-ons';
                                    })
                                    ->html(),

                                Infolists\Components\ViewEntry::make('sales_order')
                                    ->label('Sales Order PDF')
                                    ->view('filament.components.sales-order-link'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Subscriber Information')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                TextEntry::make('subscription.subscriber.name')
                                    ->label('Subscriber Name')
                                    ->url(fn () => $this->record->subscription?->subscriber ?
                                        route('filament.admin.resources.customers-companies.view', ['record' => $this->record->subscription->subscriber]) : null)
                                    ->openUrlInNewTab(),

                                TextEntry::make('subscription.billingCountry.name')
                                    ->label('Billing Country'),

                                TextEntry::make('subscription.country.name')
                                    ->label('Deployment Country'),

                                TextEntry::make('subscription.subscriber.primaryContact.email')
                                    ->label('Primary Contact Email'),

                                TextEntry::make('subscription.subscriber.primaryContact.phone_number')
                                    ->label('Primary Contact Phone'),
                            ]),
                    ]),

//                Infolists\Components\Section::make('Validation Notes')
//                    ->schema([
//                        Infolists\Components\Placeholder::make('notes')
//                            ->label('')
//                            ->content('Internal comments and notes for the finance team can be added here for decision logging.')
//                            ->extraAttributes(['class' => 'text-sm text-gray-600']),
//                    ])
//                    ->collapsible(),
            ]);
    }
}
