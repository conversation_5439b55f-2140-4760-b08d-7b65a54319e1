<?php

namespace App\Filament\Resources;

use App\Enums\PaymentRejectionReason;
use App\Enums\Subscriptions;
use App\Filament\Resources\SubscriptionReceiptResource\Pages;
use App\Models\PlatformCountry;
use App\Models\SubscriptionLicense;
use App\Models\Country;
use App\Models\SubscriptionReceipt;
use App\Services\MoneyFormatter;
use App\Services\ReceiptService;
use Cknow\Money\Money;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasShieldAccess;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Malzariey\FilamentDaterangepickerFilter\Enums\DropDirection;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class SubscriptionReceiptResource extends Resource
{
    use HasShieldAccess;

    protected static ?string $model = SubscriptionReceipt::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Payments';
    protected static ?string $navigationLabel = 'Receipt Management';
    protected static ?string $modelLabel = 'Receipt';
    protected static ?string $pluralModelLabel = 'Receipts';
    protected static ?int $navigationSort = 4;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->can('view_subscription::receipt');
    }

    public static function canCreate(): bool
    {
        return false; // Finance team cannot create receipts
    }

    public static function canEdit(Model $record): bool
    {
        return false; // Finance team cannot edit receipts directly
    }

    public static function canDelete(Model $record): bool
    {
        return false; // Finance team cannot delete receipts
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Receipt Information')
                    ->schema([
                        Forms\Components\TextInput::make('receipt_number')
                            ->label('Receipt Number')
                            ->disabled(),
                        Forms\Components\TextInput::make('amount')
                            ->label('Amount')
                            ->disabled(),
                        Forms\Components\TextInput::make('currency_code')
                            ->label('Currency')
                            ->disabled(),
                        Forms\Components\Select::make('receipt_status')
                            ->label('Status')
                            ->options([
                                'pending' => 'Pending',
                                'approved' => 'Approved',
                                'rejected' => 'Rejected',
                            ])
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('paid_at')
                            ->label('Paid At')
                            ->disabled(),
                        Forms\Components\TextInput::make('approved_by')
                            ->label('Approved By')
                            ->formatStateUsing(function ($state) {
                                if (!$state) return '-';
                                $user = \App\Models\User::find($state);
                                return $user ? $user->name : $state;
                            })
                            ->disabled(),
                        Forms\Components\TextInput::make('rejected_by')
                            ->label('Rejected By')
                            ->formatStateUsing(function ($state) {
                                if (!$state) return '-';
                                $user = \App\Models\User::find($state);
                                return $user ? $user->name : $state;
                            })
                            ->disabled(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('product_name')
                    ->label('Product')
                    ->default('Sonar')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('subscription.plan.name')
                    ->label('Subscription Plan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('subscription_type')
                    ->label('Subscription Type')
                    ->getStateUsing(function ($record) {
                        $subscription = $record->subscription;
                        if (!$subscription) return '-';

                        return $subscription->subscriptionHasFeaturePrefix('saas') ? 'SaaS' : 'Self Managed';
                    })
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'SaaS' => 'success',
                        'Self Managed' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('subscription.subscriber.name')
                    ->label('Subscriber Name')
                    ->searchable()
                    ->sortable()
                    ->url(fn ($record): string =>
                    auth()->user()->can('view_company') && $record->subscription->subscriber
                        ? route('filament.admin.resources.customers-companies.view', $record->subscription->subscriber)
                        : '#'
                    )
                    ->openUrlInNewTab(),

                Tables\Columns\TextColumn::make('subscription.billingCountry.name')
                    ->label('Billing Country')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('subscription.country.name')
                    ->label('Deployment Country')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\ViewColumn::make('receipt_preview')
                    ->label('Receipt')
                    ->view('filament.components.receipt-preview'),

                Tables\Columns\TextColumn::make('receipt_number')
                    ->label('Receipt Number')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Amount')
                    ->formatStateUsing(function ($state, $record) {
                        $priceModel = new Money($state, $record->currency_code);
                        return app(MoneyFormatter::class)->price($priceModel->divide(100)->getAmount(), $priceModel->getCurrency())->format();
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('receipt_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'warning',
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Upload Date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Paid At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('approved_by')
                    ->label('Approved By')
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $user = \App\Models\User::find($state);
                        return $user ? $user->name : $state;
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('rejected_by')
                    ->label('Rejected By')
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $user = \App\Models\User::find($state);
                        return $user ? $user->name : $state;
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('comment')
                    ->label('Comment')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->placeholder('-')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('billing_country')
                    ->label('Billing Country')
                    ->options(function () {
                        return Country::all()
                            ->pluck('name', 'id')
                            ->toArray();
                    })
                    ->query(function ($query, array $data) {
                        if (filled($data['value'])) {
                            $query->whereHas('subscription', function ($q) use ($data) {
                                $q->where('billing_country_id', $data['value']);
                            });
                        }
                    })
                    ->searchable(),

                Tables\Filters\SelectFilter::make('deployment_country')
                    ->label('Deployment Country')
                    ->options(function () {
                        return PlatformCountry::all()
                            ->pluck('name', 'platform_country_id')
                            ->toArray();
                    })
                    ->query(function ($query, array $data) {
                        if (filled($data['value'])) {
                            $query->whereHas('subscription', function ($q) use ($data) {
                                $q->where('country_id', $data['value']);
                            });
                        }
                    })
                    ->searchable(),

                Tables\Filters\Filter::make('receipt_number')
                    ->form([
                        Forms\Components\TextInput::make('receipt_number')
                            ->label('Receipt Number')
                            ->placeholder('Enter receipt number'),
                    ])
                    ->indicateUsing(fn (array $data) => filled($data['receipt_number'] ?? null)
                        ? ['label' => 'Receipt number: ' . $data['receipt_number']]
                        : null)
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['receipt_number'],
                                fn(Builder $query, $value): Builder => $query->where('receipt_number', 'like', "%{$value}%"),
                            );
                    }),
                Tables\Filters\Filter::make('subscription_id')
                    ->form([
                        Forms\Components\TextInput::make('subscription_id')
                            ->label('Subscription ID')
                            ->placeholder('Enter subscription ID'),
                    ])
                    ->indicateUsing(fn (array $data) => filled($data['subscription_id'] ?? null) ? ['label' => 'Public subscription id: ' . $data['subscription_id']] : null)
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['subscription_id'],
                                fn(Builder $query, $value): Builder => $query->whereHas('subscription', function ($q) use ($value) {
                                    $q->where('public_subscription_id', 'like', "%{$value}%");
                                }),
                            );
                    }),

                Tables\Filters\Filter::make('subscriber_name')
                    ->form([
                        Forms\Components\TextInput::make('subscriber_name')
                            ->label('Subscriber Name')
                            ->placeholder('Enter subscriber name'),
                    ])
                    ->indicateUsing(fn (array $data) => filled($data['subscriber_name'] ?? null) ? ['label' => 'Subscriber Name: ' . $data['subscriber_name']] : null)
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['subscriber_name'],
                                fn(Builder $query, $value): Builder => $query->whereHas('subscription.subscriber', function ($q) use ($value) {
                                    $q->where('name', 'like', "%{$value}%");
                                }),
                            );
                    }),

                DateRangeFilter::make('created_at') // or your specific column name
                ->label('Upload Date')
                    ->drops(DropDirection::UP) // Optional, based on your UI preference
                    ->query(function ($query, $data) {
                        $dateRange = explode(' - ', $data['created_at'] ?? '');
                        $startDate = $dateRange[0] ?? null;
                        $endDate = $dateRange[1] ?? null;

                        if (!empty($startDate) && !empty($endDate)) {
                            $startDate = Carbon::createFromFormat('d/m/Y', $startDate)->format('Y-m-d');
                            $endDate = Carbon::createFromFormat('d/m/Y', $endDate)->format('Y-m-d');

                            $query->whereBetween('created_at', [
                                $startDate . ' 00:00:00',
                                $endDate . ' 23:59:59'
                            ]);
                        }

                        return $query;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn($record) => $record->receipt_status === Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value && auth()->user()->can('update_subscription::receipt'))
                    ->form([
                        Forms\Components\Textarea::make('comment')
                            ->label('Comment (Optional)')
                            ->placeholder('Add any additional notes about the approval')
                            ->maxLength(1000)
                    ])
                    ->modalHeading('Approve Receipt')
                    ->modalDescription('Are you sure you want to approve this receipt? This will update the subscription status.')
                    ->modalSubmitActionLabel('Yes, approve receipt')
                    ->action(fn ($record, array $data) => ReceiptService::approve($record, $data)),

                Tables\Actions\Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn($record) => $record->receipt_status === Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value && auth()->user()->can('update_subscription::receipt'))
                    ->form([
                        Forms\Components\Select::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->options(PaymentRejectionReason::getValues())
                            ->required()
                            ->searchable(),
                        Forms\Components\Textarea::make('comment')
                            ->label('Additional Details')
                            ->placeholder('Provide additional details about the rejection reason')
                            ->maxLength(1000)
                    ])
                    ->action(fn ($record, array $data) => ReceiptService::reject($record, $data))
            ])
            ->bulkActions([])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptionReceipts::route('/'),
            'view' => Pages\ViewSubscriptionReceipt::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'subscription.plan',
                'subscription.subscriber',
                'subscription.billingCountry',
                'subscription.country',
                'subscription.currency'
            ]);
    }

}
