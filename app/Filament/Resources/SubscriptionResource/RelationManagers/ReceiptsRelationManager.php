<?php

namespace App\Filament\Resources\SubscriptionResource\RelationManagers;

use App\Enums\PaymentRejectionReason;
use App\Enums\SubscriptionReceipts;
use App\Enums\Subscriptions;
use App\Services\MoneyFormatter;
use App\Services\ReceiptService;
use Cknow\Money\Money;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ReceiptsRelationManager extends RelationManager
{
    protected static string $relationship = 'subscriptionReceipts';

    protected static ?string $recordTitleAttribute = 'receipt_number';

    public static function canViewForRecord(\Illuminate\Database\Eloquent\Model $ownerRecord, string $pageClass): bool
    {
        return auth()->user()->can('view_subscription::receipt');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ViewColumn::make('receipt_preview')
                    ->label('Receipt')
                    ->view('filament.components.receipt-preview'),
                Tables\Columns\TextColumn::make('receipt_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->formatStateUsing(function ($state, $record) {
                        $priceModel = new Money($state, $record->currency_code);
                        return app(MoneyFormatter::class)->price($priceModel->divide(100)->getAmount(), $priceModel->getCurrency())->format();
                    })
                    ->sortable(),
                // Tables\Columns\TextColumn::make('currency_code')
                //     ->searchable(),
                Tables\Columns\TextColumn::make('receipt_status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'warning',
                    }),
                Tables\Columns\TextColumn::make('paid_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('approved_by')
                    ->toggleable()
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $user = \App\Models\User::find($state);
                        return $user ? $user->name : $state;
                    }),

                Tables\Columns\TextColumn::make('rejected_by')
                    ->toggleable()
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $user = \App\Models\User::find($state);
                        return $user ? $user->name : $state;
                    }),

                Tables\Columns\TextColumn::make('comment')
                    ->label('Comment')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->placeholder('-')
                    ->toggleable(),

                // todo:nc check if we need to show payment type? and what is the relation? (add relation in receipt)
                //                Tables\Columns\TextColumn::make('subscription.subscriptionLatestPayment.payment_type')
                //                    ->label('Payment Type')
                //                    ->formatStateUsing(function ($state, $record) {
                //                        if (empty($state)) {
                //                            return 'Not specified';
                //                        }
                //                        return ucfirst(str_replace('_', ' ', $state));
                //                    })
                //                    ->searchable()
                //                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('uploadReceipt')
                    ->label('Upload Receipt')
                    ->icon('heroicon-o-paper-clip')
                    ->form([
                        Forms\Components\FileUpload::make('receipt_file')
                            ->label('Receipt File')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'])
                            ->maxSize(5120)
                            ->disk('public') // Store temporarily in public disk
                            ->directory('temp-uploads')
                            ->required()
                    ])
                    ->action(function (array $data) {
                        $subscription = $this->getOwnerRecord();
                        // Create receipt record
                        $receiptModel = $subscription->subscriptionReceipts()->create([
                            'receipt_number' => uniqid(), // todo:nc check if there is specific format for receipt number
                            'amount' => $subscription->totalPrice(asArray: false),
                            'currency_code' => $subscription->plan->currency,
                            'receipt_status' => SubscriptionReceipts::RECEIPT_STATUS_PENDING->value,
                            'storage' => 'gcs',
                        ]);

                        // Handle file upload - Filament returns the path to the uploaded file
                        $filePath = $data['receipt_file'];

                        // Get the full path to the uploaded file
                        $fullPath = storage_path('app/public/' . $filePath);

                        // Generate receipt name
                        $receiptName = $subscription->subscriber->public_company_id . "_" . $subscription->public_subscription_id . "_" . $receiptModel->subscription_receipts_id;

                        // Get file contents
                        $fileContents = file_get_contents($fullPath);
                        $extension = pathinfo($fullPath, PATHINFO_EXTENSION);

                        // Create a temporary file with the contents
                        $tempFile = tempnam(sys_get_temp_dir(), 'receipt');
                        file_put_contents($tempFile, $fileContents);

                        // Create an UploadedFile instance
                        $file = new \Illuminate\Http\UploadedFile(
                            $tempFile,
                            basename($fullPath),
                            mime_content_type($tempFile),
                            null,
                            true
                        );

                        // Upload to GCS
                        $gcsService = app(\App\Services\GcsService::class)->setFolder('receipts')->setFileName($receiptName);
                        $receipt_path = $gcsService->upload($file);

                        // Clean up temporary files
                        @unlink($tempFile);
                        @unlink($fullPath);

                        // Update receipt with path
                        $receiptModel->update([
                            'receipt_path' => $receipt_path ?? '',
                        ]);

                        // Update subscription status if needed
                        if ($subscription->subscription_status === Subscriptions::SUBSCRIPTION_STATUS_PENDING_RECEIPT->value) {
                            /**
                             * Set Observer EventType
                             */
                            \App\Observers\SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_PAYMENT_UPDATE->value;

                            $subscription->update([
                                'subscription_status' => Subscriptions::SUBSCRIPTION_STATUS_PENDING_PAYMENT->value,
                                'payment_status' => Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_IN_REVIEW->value,
                            ]);
                        }

                        Notification::make()
                            ->title('Receipt Uploaded')
                            ->body('The receipt has been uploaded successfully and is pending review.')
                            ->success()
                            ->send();
                    })
                    ->visible(fn() => auth()->user()->can('create_subscription::receipt')),
            ])
            ->actions([
                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn($record) => $record->receipt_status === Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value && auth()->user()->can('update_subscription::receipt'))
                    ->form([
                        Forms\Components\Textarea::make('comment')
                            ->label('Comment (Optional)')
                            ->placeholder('Add any additional notes about the approval')
                            ->maxLength(1000)
                    ])
                    ->modalHeading('Approve Receipt')
                    ->modalDescription('Are you sure you want to approve this receipt? This will update the subscription status.')
                    ->modalSubmitActionLabel('Yes, approve receipt')
                    ->action(fn ($record, array $data): bool => ReceiptService::approve($record, $data)),

                Tables\Actions\Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn($record) => $record->receipt_status === Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING->value && auth()->user()->can('update_subscription::receipt'))
                    ->form([
                        Forms\Components\Select::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->options(PaymentRejectionReason::getValues())
                            ->required()
                            ->searchable(),
                        Forms\Components\Textarea::make('comment')
                            ->label('Additional Details')
                            ->placeholder('Provide additional details about the rejection reason')
                            ->maxLength(1000)
                    ])
                    ->action(fn ($record, array $data): bool => ReceiptService::reject($record, $data)),
            ]);
    }
}
