steps:
  # Step 1: Authenticate with Artifact Registry
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo $PROJECT_ID
        gcloud auth configure-docker ${_REGION}-docker.pkg.dev

  # Step 2: sonar scanner cli
  - name: 'sonarsource/sonar-scanner-cli'
    entrypoint: 'bash'
    secretEnv: ['INTERNAL_SONAR_TOKEN_ADMIN']
    args:
      - '-c'
      - |
        mkdir -P /tmp/.sonar
        mkdir -P /tmp/.scannerwork
        export SONAR_USER_HOME=/tmp/.sonar
        sonar-scanner \
         -Dsonar.projectKey=ismena-sonarqube-admins \
         -Dsonar.projectName=ismena-sonarqube-admins \
         -Dsonar.sources=. \
         -Dsonar.host.url=https://sonar-internal.ismena.me \
         -Dsonar.login=$$INTERNAL_SONAR_TOKEN_ADMIN \
         -Dsonar.working.directory=/tmp/.scannerwork

  # Step 3: Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    entrypoint: 'bash'
    secretEnv: 
    - AUTH_COMPOSER
    args:
    - '-c'
    - |
      docker build \
        -f docker/dockerfile-php \
        -t "${_REGION}-docker.pkg.dev/${_PROJECT_ID_JAWDA}/${_REPO_NAME_JAWDA}/${_IMAGE_NAME_JAWDA}:latest" \
        -t "${_REGION}-docker.pkg.dev/${_PROJECT_ID_JAWDA}/${_REPO_NAME_JAWDA}/${_IMAGE_NAME_JAWDA}:$COMMIT_SHA" \
        --build-arg AUTH_COMPOSER="$$AUTH_COMPOSER" \
        . 

      
  # Step 4: Push the Docker image with "latest" tag to Jawda registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'push',
        '${_REGION}-docker.pkg.dev/${_PROJECT_ID_JAWDA}/${_REPO_NAME_JAWDA}/${_IMAGE_NAME_JAWDA}:latest',
      ]

  # Step 5: Push the Docker image with the commit hash tag to Jawda registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'push',
        '${_REGION}-docker.pkg.dev/${_PROJECT_ID_JAWDA}/${_REPO_NAME_JAWDA}/${_IMAGE_NAME_JAWDA}:$COMMIT_SHA',
      ]

  # Step 5: Substitute image ID and commit the change for Jawda project
  - name: 'gcr.io/cloud-builders/git'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Cloning repository..."
        echo "Repo URL: https://github.com/${_GITHUB_ORG}/${_GITHUB_REPO_JAWDA}.git"
        git clone https://$$<EMAIL>/${_GITHUB_ORG}/${_GITHUB_REPO_JAWDA}.git

        echo "Changing directory into manifests directory"
        cd ${_GITHUB_REPO_JAWDA}/${_MANIFESTS_DIR_JAWDA}
        echo "Current directory: $(pwd)"

        echo "Before updating:"
        grep 'image:' ${_DEPLOYMENT_FILE_JAWDA}.yaml
        grep 'image:' ${_CRONJOB_FILE_JAWDA}.yaml
        grep 'image:' ${_CRONJOB_FILE_UPDATE_USAGE_JAWDA}.yaml
        grep 'image:' ${_CRONJOB_FILE_SUBSCRIPTIONS}.yaml

        echo "Updating image ID in ${_DEPLOYMENT_FILE_JAWDA}.yaml..."
        sed -i "s|\(image: \).*${_IMAGE_NAME_JAWDA}:.*|\1${_REGION}-docker.pkg.dev/${_PROJECT_ID_JAWDA}/${_REPO_NAME_JAWDA}/${_IMAGE_NAME_JAWDA}:$COMMIT_SHA|" ${_DEPLOYMENT_FILE_JAWDA}.yaml

        echo "Updating image ID in ${_CRONJOB_FILE_JAWDA}.yaml..."
        sed -i "s|\(image: \).*${_IMAGE_NAME_JAWDA}:.*|\1${_REGION}-docker.pkg.dev/${_PROJECT_ID_JAWDA}/${_REPO_NAME_JAWDA}/${_IMAGE_NAME_JAWDA}:$COMMIT_SHA|" ${_CRONJOB_FILE_JAWDA}.yaml

        echo "Updating image ID in ${_CRONJOB_FILE_UPDATE_USAGE_JAWDA}.yaml..."
        sed -i "s|\(image: \).*${_IMAGE_NAME_JAWDA}:.*|\1${_REGION}-docker.pkg.dev/${_PROJECT_ID_JAWDA}/${_REPO_NAME_JAWDA}/${_IMAGE_NAME_JAWDA}:$COMMIT_SHA|" ${_CRONJOB_FILE_UPDATE_USAGE_JAWDA}.yaml

        echo "Updating image ID in ${_CRONJOB_FILE_SUBSCRIPTIONS}.yaml..."
        sed -i "s|\(image: \).*${_IMAGE_NAME_JAWDA}:.*|\1${_REGION}-docker.pkg.dev/${_PROJECT_ID_JAWDA}/${_REPO_NAME_JAWDA}/${_IMAGE_NAME_JAWDA}:$COMMIT_SHA|" ${_CRONJOB_FILE_SUBSCRIPTIONS}.yaml

        echo "After updating:"
        grep 'image:' ${_DEPLOYMENT_FILE_JAWDA}.yaml
        grep 'image:' ${_CRONJOB_FILE_JAWDA}.yaml
        grep 'image:' ${_CRONJOB_FILE_UPDATE_USAGE_JAWDA}.yaml
        grep 'image:' ${_CRONJOB_FILE_SUBSCRIPTIONS}.yaml

        echo "Configuring Git user..."
        git config --global user.email "${_GITHUB_EMAIL}"
        git config --global user.name "${_GITHUB_USER}"

        echo "Checking for changes..."
        git status
        git diff

        echo "Committing changes..."
        git add ${_DEPLOYMENT_FILE_JAWDA}.yaml
        git add ${_CRONJOB_FILE_JAWDA}.yaml
        git add ${_CRONJOB_FILE_UPDATE_USAGE_JAWDA}.yaml
        git add ${_CRONJOB_FILE_SUBSCRIPTIONS}.yaml
        git commit -m "Update image ID to $COMMIT_SHA" || echo "Nothing to commit"

        echo "Pushing changes..."
        git push origin main

        echo "Changes pushed; refresh ArgoCD or wait 3 minutes"
    secretEnv: ['GITHUB_TOKEN']

availableSecrets:
  secretManager:
    - versionName: projects/378947332109/secrets/GITHUB_TOKEN/versions/latest
      env: 'GITHUB_TOKEN'
    - versionName: projects/378947332109/secrets/AUTH_COMPOSER/versions/latest
      env: 'AUTH_COMPOSER'
    - versionName: projects/378947332109/secrets/INTERNAL_SONAR_TOKEN_ADMIN/versions/latest
      env: 'INTERNAL_SONAR_TOKEN_ADMIN'

substitutions:
  COMMIT_SHA: $BUILD_ID
  _GITHUB_ORG: isolutions-sa
  _GITHUB_REPO_JAWDA: sonar-k8s-manifest
  _GITHUB_REPO_PLATFORMS: isolution-platforms-k8s-manifest
  _GITHUB_USER: devops.ismena
  _GITHUB_EMAIL: <EMAIL>
  _MANIFESTS_DIR_JAWDA: k8s-manifest/sonar-admin
  _DEPLOYMENT_FILE_JAWDA: sonar-admin
  _CRONJOB_FILE_JAWDA: sonar-admin-cron-job
  _CRONJOB_FILE_UPDATE_USAGE_JAWDA: sonar-admin-cron-job-update-usage
  _CRONJOB_FILE_SUBSCRIPTIONS: sonar-admin-cron-job-subscriptions-expire 
  _REGION: europe-west1
  _PROJECT_ID_JAWDA: prj-sonarbyisolutions-0425
  _REPO_NAME_JAWDA: sonar-repo
  _IMAGE_NAME_JAWDA: sonar-admin

options:
  machineType: E2_HIGHCPU_8
  logging: CLOUD_LOGGING_ONLY
  env:
    - PROJECT_ID=prj-sonarbyisolutions-0425